import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import Login from './components/Login';
import AdminDashboard from './pages/AdminDashboard';
import UsuarioDashboard from './pages/UsuarioDashboard';
import RutaProtegida from './components/RutaProtegida';
import RedirectIfAuthenticated from './components/RedirectIfAuthenticated';
import { LanguageProvider } from './i18n';

function App() {
  return (
    <LanguageProvider>
      <Router>
        <Routes>
          <Route path="/" element={
            <RedirectIfAuthenticated>
              <Login />
            </RedirectIfAuthenticated>
          } />

          <Route
            path="/admin"
            element={
              <RutaProtegida requiredRole="admin">
                <AdminDashboard />
              </RutaProtegida>
            }
          />

          <Route
            path="/usuario"
            element={
              <RutaProtegida requiredRole="usuario">
                <UsuarioDashboard />
              </RutaProtegida>
            }
          />
        </Routes>
      </Router>
      <Toaster position="bottom-center" />
    </LanguageProvider>
  );
}

export default App;
