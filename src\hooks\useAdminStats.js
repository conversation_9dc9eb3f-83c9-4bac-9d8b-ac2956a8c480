import { useState, useEffect } from 'react';

export const useAdminStats = () => {
  const [stats, setStats] = useState({
    totalUsers: 0,
    activeUsers: 0,
    activeKeys: 0,
    totalKeys: 0
  });
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);

  // Datos simulados de estadísticas
  const mockStats = {
    totalUsers: 156,
    activeUsers: 142,
    activeKeys: 89,
    totalKeys: 234
  };

  useEffect(() => {
    // Cargar estadísticas iniciales
    setStats(mockStats);
  }, []);

  const loadStats = async () => {
    setIsLoading(true);
    setError(null);
    try {
      // Simular llamada a API
      await new Promise(resolve => setTimeout(resolve, 500));
      setStats(mockStats);
    } catch (err) {
      setError('Error al cargar las estadísticas');
    } finally {
      setIsLoading(false);
    }
  };

  const clearError = () => {
    setError(null);
  };

  return {
    stats,
    isLoading,
    error,
    loadStats,
    clearError
  };
};
