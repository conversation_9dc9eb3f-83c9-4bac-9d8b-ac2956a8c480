import PropTypes from 'prop-types';

/**
 * Componente MiniStatCard para mostrar estadísticas pequeñas
 */
const MiniStatCard = ({
  title,
  value,
  icon: Icon,
  color = 'blue',
  darkMode = false
}) => {
  const colorClasses = {
    blue: 'text-blue-600 bg-blue-100 dark:bg-blue-900 dark:text-blue-300',
    green: 'text-green-600 bg-green-100 dark:bg-green-900 dark:text-green-300',
    yellow: 'text-yellow-600 bg-yellow-100 dark:bg-yellow-900 dark:text-yellow-300',
    red: 'text-red-600 bg-red-100 dark:bg-red-900 dark:text-red-300',
    purple: 'text-purple-600 bg-purple-100 dark:bg-purple-900 dark:text-purple-300'
  };

  return (
    <div className={`p-4 rounded-xl border transition-all duration-300 ${
      darkMode 
        ? 'bg-gray-800 border-gray-700 hover:bg-gray-750' 
        : 'bg-white border-gray-200 hover:shadow-md'
    }`}>
      <div className="flex items-center justify-between">
        <div>
          <p className={`text-sm font-medium ${
            darkMode ? 'text-gray-300' : 'text-gray-600'
          }`}>
            {title}
          </p>
          <p className={`text-2xl font-bold mt-1 ${
            darkMode ? 'text-white' : 'text-gray-900'
          }`}>
            {value}
          </p>
        </div>
        {Icon && (
          <div className={`p-3 rounded-lg ${colorClasses[color]}`}>
            <Icon size={24} />
          </div>
        )}
      </div>
    </div>
  );
};

MiniStatCard.propTypes = {
  title: PropTypes.string.isRequired,
  value: PropTypes.oneOfType([PropTypes.string, PropTypes.number]).isRequired,
  icon: PropTypes.elementType,
  color: PropTypes.oneOf(['blue', 'green', 'yellow', 'red', 'purple']),
  darkMode: PropTypes.bool
};

export default MiniStatCard;
