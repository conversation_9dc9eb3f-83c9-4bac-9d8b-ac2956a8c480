import { useState, useEffect } from 'react';

export const useUsers = () => {
  const [users, setUsers] = useState([]);
  const [usersWithKeys, setUsersWithKeys] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);

  // Datos simulados de usuarios
  const mockUsers = [
    {
      id: 1,
      firstName: "<PERSON>",
      lastName: "<PERSON>",
      email: "<EMAIL>",
      role: "usuario",
      status: "activo",
      joinDate: "2024-01-15",
      lastLogin: "2024-07-08",
      keys: [
        { id: "qkey_001_jp", name: "<PERSON><PERSON><PERSON>", status: "activa" },
        { id: "qkey_002_jp", name: "<PERSON><PERSON><PERSON>arroll<PERSON>", status: "activa" }
      ]
    },
    {
      id: 2,
      firstName: "<PERSON>",
      lastName: "<PERSON>",
      email: "<EMAIL>",
      role: "usuario",
      status: "activo",
      joinDate: "2024-02-10",
      lastLogin: "2024-07-09",
      keys: [
        { id: "qkey_003_mg", name: "Llave Test", status: "inactiva" }
      ]
    },
    {
      id: 3,
      firstName: "Carlos",
      lastName: "Rodríguez",
      email: "<EMAIL>",
      role: "usuario",
      status: "inactivo",
      joinDate: "2024-03-05",
      lastLogin: "2024-06-20",
      keys: []
    }
  ];

  useEffect(() => {
    // Cargar usuarios iniciales
    setUsers(mockUsers);
    setUsersWithKeys(mockUsers);
  }, []);

  const getAllUsers = async () => {
    setIsLoading(true);
    setError(null);
    try {
      // Simular llamada a API
      await new Promise(resolve => setTimeout(resolve, 500));
      setUsers(mockUsers);
      setUsersWithKeys(mockUsers);
    } catch (err) {
      setError('Error al cargar los usuarios');
    } finally {
      setIsLoading(false);
    }
  };

  const createUser = async (userData) => {
    setIsLoading(true);
    setError(null);
    try {
      // Simular creación de usuario
      await new Promise(resolve => setTimeout(resolve, 500));
      
      const newUser = {
        id: users.length + 1,
        firstName: userData.firstName || userData.name?.split(' ')[0] || '',
        lastName: userData.lastName || userData.name?.split(' ').slice(1).join(' ') || '',
        email: userData.email,
        role: userData.role || 'usuario',
        status: 'activo',
        joinDate: new Date().toISOString().split('T')[0],
        lastLogin: null,
        keys: []
      };

      const updatedUsers = [...users, newUser];
      setUsers(updatedUsers);
      setUsersWithKeys(updatedUsers);
      
      return { success: true, user: newUser };
    } catch (err) {
      setError('Error al crear el usuario');
      return { success: false, error: err.message };
    } finally {
      setIsLoading(false);
    }
  };

  const updateUser = async (userId, userData) => {
    setIsLoading(true);
    setError(null);
    try {
      // Simular actualización de usuario
      await new Promise(resolve => setTimeout(resolve, 500));
      
      const updatedUsers = users.map(user => 
        user.id === userId ? { ...user, ...userData } : user
      );
      
      setUsers(updatedUsers);
      setUsersWithKeys(updatedUsers);
      
      return { success: true };
    } catch (err) {
      setError('Error al actualizar el usuario');
      return { success: false, error: err.message };
    } finally {
      setIsLoading(false);
    }
  };

  const deleteUser = async (userId) => {
    setIsLoading(true);
    setError(null);
    try {
      // Simular eliminación de usuario
      await new Promise(resolve => setTimeout(resolve, 500));
      
      const updatedUsers = users.filter(user => user.id !== userId);
      setUsers(updatedUsers);
      setUsersWithKeys(updatedUsers);
      
      return { success: true };
    } catch (err) {
      setError('Error al eliminar el usuario');
      return { success: false, error: err.message };
    } finally {
      setIsLoading(false);
    }
  };

  const clearError = () => {
    setError(null);
  };

  return {
    users,
    usersWithKeys,
    isLoading,
    error,
    getAllUsers,
    createUser,
    updateUser,
    deleteUser,
    clearError
  };
};
