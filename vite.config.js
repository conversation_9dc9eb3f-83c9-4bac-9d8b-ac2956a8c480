import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'

// https://vite.dev/config/
export default defineConfig({
  plugins: [react()],

  // 🛡️ Configuración de seguridad para desarrollo
  server: {
    headers: {
      // Previene clickjacking
      'X-Frame-Options': 'DENY',

      // Previene MIME type sniffing
      'X-Content-Type-Options': 'nosniff',

      // Controla información del referrer
      'Referrer-Policy': 'strict-origin-when-cross-origin',

      // Deshabilita APIs peligrosas
      'Permissions-Policy': 'camera=(), microphone=(), geolocation=(), payment=(), usb=(), magnetometer=(), gyroscope=(), accelerometer=()',

      // Previene DNS prefetching
      'X-DNS-Prefetch-Control': 'off',

      // Fuerza HTTPS en producción (solo para desarrollo local)
      'Strict-Transport-Security': 'max-age=31536000; includeSubDomains',

      // Headers adicionales de seguridad
      'X-XSS-Protection': '1; mode=block',
      'X-Permitted-Cross-Domain-Policies': 'none',
      'Cross-Origin-Embedder-Policy': 'require-corp',
      'Cross-Origin-Opener-Policy': 'same-origin',
      'Cross-Origin-Resource-Policy': 'same-origin'
    }
  },

  // 🔒 Configuración de build para producción
  build: {
    // Minificar para ofuscar código
    minify: 'terser',
    terserOptions: {
      compress: {
        drop_console: true, // Remover console.logs en producción
        drop_debugger: true // Remover debugger statements
      }
    },

    // Generar source maps solo en desarrollo la verdad
    sourcemap: false,

    // Configuración de chunks para mejor seguridad
    rollupOptions: {
      output: {
        // Ofuscar nombres de archivos
        entryFileNames: 'assets/[name]-[hash].js',
        chunkFileNames: 'assets/[name]-[hash].js',
        assetFileNames: 'assets/[name]-[hash].[ext]'
      }
    }
  }
})
