// src/components/RutaProtegida.jsx
import { Navigate } from 'react-router-dom';

// 🛡️ Gestión segura de tokens (duplicada para consistencia)
const TokenManager = {
  isTokenValid: () => {
    const token = sessionStorage.getItem('token');
    const expiry = sessionStorage.getItem('tokenExpiry');

    if (!token || !expiry) {
      // Verificar también localStorage legacy
      const legacyToken = localStorage.getItem('token');
      if (legacyToken) {
        // Migrar a sessionStorage
        sessionStorage.setItem('token', legacyToken);
        sessionStorage.setItem('userRole', localStorage.getItem('userRole') || 'usuario');
        const newExpiry = Date.now() + (24 * 60 * 60 * 1000);
        sessionStorage.setItem('tokenExpiry', newExpiry.toString());

        // Limpiar localStorage
        localStorage.removeItem('token');
        localStorage.removeItem('userRole');

        return true;
      }
      return false;
    }

    if (Date.now() > parseInt(expiry)) {
      TokenManager.clearToken();
      return false;
    }

    return true;
  },

  clearToken: () => {
    sessionStorage.removeItem('token');
    sessionStorage.removeItem('userRole');
    sessionStorage.removeItem('tokenExpiry');
    sessionStorage.removeItem('loginTime');
    localStorage.removeItem('token'); // Limpiar también localStorage legacy
    localStorage.removeItem('userRole');
  },

  getUserRole: () => {
    return sessionStorage.getItem('userRole') || localStorage.getItem('userRole');
  }
};

// 🔒 Logging de seguridad para rutas
const logSecurityEvent = (event, details) => {
  const timestamp = new Date().toISOString();
  console.log(`[ROUTE_SECURITY] ${timestamp} - ${event}:`, details);
};

const RutaProtegida = ({ children, requiredRole }) => {
  const isTokenValid = TokenManager.isTokenValid();
  const userRole = TokenManager.getUserRole();

  // Si no hay token válido, redirigir al login
  if (!isTokenValid) {
    logSecurityEvent('UNAUTHORIZED_ACCESS_ATTEMPT', {
      requiredRole,
      reason: 'No valid token'
    });
    return <Navigate to="/" replace />;
  }

  // Si se requiere un rol específico y el usuario no lo tiene
  if (requiredRole && userRole !== requiredRole) {
    logSecurityEvent('ROLE_MISMATCH_ACCESS_ATTEMPT', {
      requiredRole,
      userRole,
      timestamp: new Date().toISOString()
    });

    // Redirigir al dashboard correspondiente según el rol del usuario
    if (userRole === 'admin') {
      return <Navigate to="/admin" replace />;
    } else if (userRole === 'usuario') {
      return <Navigate to="/usuario" replace />;
    } else {
      // Si no tiene rol válido, cerrar sesión y redirigir al login
      logSecurityEvent('INVALID_ROLE_DETECTED', {
        userRole,
        action: 'Force logout'
      });
      TokenManager.clearToken();
      return <Navigate to="/" replace />;
    }
  }

  // Log de acceso exitoso
  logSecurityEvent('AUTHORIZED_ACCESS', {
    requiredRole,
    userRole,
    route: window.location.pathname
  });

  return children;
};

export default RutaProtegida;