@tailwind base;
@tailwind components;
@tailwind utilities;

body {
  overflow-y: hidden;
}

<<<<<<< Updated upstream
/* Scrollbar moderno y elegante - Opción 4 */
.scrollbar-thin {
  scrollbar-width: thin;
  scrollbar-color: #9ca3af rgba(243, 244, 246, 0.5);
}

/* Webkit browsers (Chrome, Safari, Edge) */
.scrollbar-thin::-webkit-scrollbar {
  width: 6px;
}

.scrollbar-thin::-webkit-scrollbar-track {
  background: rgba(243, 244, 246, 0.3);
  border-radius: 12px;
  margin: 4px;
}

.scrollbar-thin::-webkit-scrollbar-thumb {
  background: linear-gradient(180deg, #9ca3af 0%, #6b7280 100%);
  border-radius: 12px;
  transition: all 0.3s ease;
  box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.1);
}

.scrollbar-thin::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(180deg, #6b7280 0%, #4b5563 100%);
  transform: scaleY(1.1);
  box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.2);
}

/* Dark mode scrollbar */
.dark .scrollbar-thin {
  scrollbar-color: #6b7280 rgba(55, 65, 81, 0.5);
}

.dark .scrollbar-thin::-webkit-scrollbar-track {
  background: rgba(55, 65, 81, 0.3);
}

.dark .scrollbar-thin::-webkit-scrollbar-thumb {
  background: linear-gradient(180deg, #6b7280 0%, #4b5563 100%);
}

.dark .scrollbar-thin::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(180deg, #9ca3af 0%, #6b7280 100%);
}

/* Dark mode scroll */
.dark .scrollbar-thin::-webkit-scrollbar-thumb {
  background: #4b5563;
}

.dark .scrollbar-thin::-webkit-scrollbar-thumb:hover {
  background: #374151;
}

/* Animaciones para el scroll */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.animate-fadeIn {
  animation: fadeIn 0.3s ease-out;
}

.animate-slideUp {
  animation: slideUp 0.4s ease-out;
}
=======
/* Animación RGB para el reporte de auto-certificación */
@keyframes rgbGlow {
  0% {
    background-position: 0% 50%;
    box-shadow:
      0 0 20px rgba(255, 0, 102, 0.3),
      0 0 40px rgba(0, 255, 255, 0.2),
      0 0 60px rgba(255, 102, 0, 0.1);
  }
  25% {
    background-position: 100% 50%;
    box-shadow:
      0 0 20px rgba(0, 255, 255, 0.3),
      0 0 40px rgba(255, 102, 0, 0.2),
      0 0 60px rgba(255, 0, 102, 0.1);
  }
  50% {
    background-position: 100% 100%;
    box-shadow:
      0 0 20px rgba(255, 102, 0, 0.3),
      0 0 40px rgba(255, 0, 102, 0.2),
      0 0 60px rgba(0, 255, 255, 0.1);
  }
  75% {
    background-position: 0% 100%;
    box-shadow:
      0 0 20px rgba(255, 0, 102, 0.3),
      0 0 40px rgba(0, 255, 255, 0.2),
      0 0 60px rgba(255, 102, 0, 0.1);
  }
  100% {
    background-position: 0% 50%;
    box-shadow:
      0 0 20px rgba(255, 0, 102, 0.3),
      0 0 40px rgba(0, 255, 255, 0.2),
      0 0 60px rgba(255, 102, 0, 0.1);
  }
}

