import { useState, useEffect } from 'react';

export const useKeys = () => {
  const [keys, setKeys] = useState([]);
  const [statistics, setStatistics] = useState({
    total: 0,
    uploadedToCtm: 0,
    active: 0
  });
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);

  // Datos simulados de llaves
  const mockKeys = [
    {
      id: "qkey_001_jp",
      name: "Llave Principal",
      created: "2024-01-15",
      lastUsed: "2024-07-08",
      status: "UPLOADED_TO_CTM",
      uploadedToCtm: true,
      type: "QRNG-256",
      uses: 1247
    },
    {
      id: "qkey_002_jp",
      name: "Llave Desarrollo",
      created: "2024-02-10",
      lastUsed: "2024-07-09",
      status: "UPLOADED_TO_CTM",
      uploadedToCtm: true,
      type: "QRNG-128",
      uses: 856
    },
    {
      id: "qkey_003_jp",
      name: "Llave Test",
      created: "2024-03-05",
      lastUsed: "2024-06-20",
      status: "INACTIVE",
      uploadedToCtm: false,
      type: "QRNG-64",
      uses: 23
    }
  ];

  useEffect(() => {
    // Cargar llaves iniciales
    setKeys(mockKeys);
    updateStatistics(mockKeys);
  }, []);

  const updateStatistics = (keysList) => {
    setStatistics({
      total: keysList.length,
      uploadedToCtm: keysList.filter(key => key.uploadedToCtm || key.status === 'UPLOADED_TO_CTM').length,
      active: keysList.filter(key => key.status === 'UPLOADED_TO_CTM' || key.status === 'ACTIVE').length
    });
  };

  const getKeysByUser = async (userId) => {
    setIsLoading(true);
    setError(null);
    try {
      // Simular llamada a API
      await new Promise(resolve => setTimeout(resolve, 500));
      setKeys(mockKeys);
      updateStatistics(mockKeys);
    } catch (err) {
      setError('Error al cargar las llaves del usuario');
    } finally {
      setIsLoading(false);
    }
  };

  const uploadToCTM = async (keyData) => {
    setIsLoading(true);
    setError(null);
    try {
      // Simular subida a CTM
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      const newKey = {
        id: `qkey_${Date.now()}`,
        name: keyData.name || `Llave ${keys.length + 1}`,
        created: new Date().toISOString().split('T')[0],
        lastUsed: "Nunca",
        status: "UPLOADED_TO_CTM",
        uploadedToCtm: true,
        type: "QRNG-256",
        uses: 0
      };

      const updatedKeys = [...keys, newKey];
      setKeys(updatedKeys);
      updateStatistics(updatedKeys);
      
      return { success: true, key: newKey };
    } catch (err) {
      setError('Error al subir la llave a CTM');
      return { success: false, error: err.message };
    } finally {
      setIsLoading(false);
    }
  };

  const deleteKey = async (keyId) => {
    setIsLoading(true);
    setError(null);
    try {
      // Simular eliminación
      await new Promise(resolve => setTimeout(resolve, 500));
      
      const updatedKeys = keys.filter(key => key.id !== keyId);
      setKeys(updatedKeys);
      updateStatistics(updatedKeys);
      
      return { success: true };
    } catch (err) {
      setError('Error al eliminar la llave');
      return { success: false, error: err.message };
    } finally {
      setIsLoading(false);
    }
  };

  const clearError = () => {
    setError(null);
  };

  return {
    keys,
    statistics,
    isLoading,
    error,
    getKeysByUser,
    uploadToCTM,
    deleteKey,
    clearError
  };
};
