import { useState } from 'react';
import { Plus, Edit, Trash2, Key, Eye } from 'lucide-react';
import PropTypes from 'prop-types';
import { useLanguage } from '../../../i18n/LanguageContext';
import { <PERSON><PERSON>, LoadingSpinner, Error<PERSON><PERSON>t } from '../../common';
import UserCreateModal from './UserCreateModal';
import UserEditModal from './UserEditModal';
import UserDeleteModal from './UserDeleteModal';
import UserKeysModal from './UserKeysModal';

/**
 * Componente para gestión de usuarios en el AdminDashboard
 */
const UserManagement = ({
  users,
  usersWithKeys,
  isLoading,
  error,
  onClearError,
  onUpdateUser,
  onCreateUser,
  onDeleteUser,
  onGetUserKeys,
  darkMode
}) => {
  const { t } = useLanguage();
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [showKeysModal, setShowKeysModal] = useState(false);
  const [selectedUser, setSelectedUser] = useState(null);
  const [userKeys, setUserKeys] = useState([]);
  const [keysLoading, setKeysLoading] = useState(false);

  const handleCreateUser = async (userData) => {
    await onCreateUser(userData);
    setShowCreateModal(false);
  };

  const handleEditUser = (user) => {
    setSelectedUser(user);
    setShowEditModal(true);
  };

  const handleUpdateUser = async (userData) => {
    await onUpdateUser(selectedUser.id, userData);
    setShowEditModal(false);
    setSelectedUser(null);
  };

  const handleDeleteUser = (user) => {
    setSelectedUser(user);
    setShowDeleteModal(true);
  };

  const handleConfirmDelete = async () => {
    await onDeleteUser(selectedUser.id);
    setShowDeleteModal(false);
    setSelectedUser(null);
  };

  const handleViewUserKeys = async (user) => {
    setSelectedUser(user);
    setKeysLoading(true);
    setShowKeysModal(true);
    
    try {
      const keys = await onGetUserKeys(user.id);
      setUserKeys(keys);
    } catch (error) {
      console.error('Error loading user keys:', error);
      setUserKeys([]);
    } finally {
      setKeysLoading(false);
    }
  };

  const handleCloseKeysModal = () => {
    setShowKeysModal(false);
    setSelectedUser(null);
    setUserKeys([]);
  };

  return (
    <div className="space-y-6">
      {/* Header con botón de crear usuario */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-light tracking-wide leading-relaxed mb-2 text-gray-900 dark:text-white">
            {t('users.title')}
          </h1>
          <p className="text-gray-600 dark:text-gray-300 font-light tracking-wide">
            {t('users.subtitle')}
          </p>
        </div>
        
        <Button
          onClick={() => setShowCreateModal(true)}
          className="flex items-center gap-2 bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors"
          disabled={isLoading}
        >
          <Plus size={16} />
          {t('users.createUser')}
        </Button>
      </div>

      <ErrorAlert error={error} onClose={onClearError} />

      {isLoading ? (
        <LoadingSpinner message={t('users.loadingUsers')} />
      ) : (
        <div className="space-y-4">
          {users.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              {t('users.noUsers')}
            </div>
          ) : (
            users.map((user) => (
              <div
                key={user.id}
                className={`p-4 rounded-xl border shadow-md hover:shadow-lg transition-all duration-300 ${
                  darkMode ? 'bg-gray-700 border-gray-600' : 'bg-white border-gray-200'
                }`}
              >
                <div className="flex justify-between items-start">
                  <div className="flex-1">
                    <div className="flex items-center gap-4 mb-3">
                      <h3 className="text-lg font-semibold">
                        {user.firstName} {user.lastName}
                      </h3>
                      <span
                        className={`px-3 py-1 rounded-full text-xs font-bold ${
                          user.status === 'activo'
                            ? 'bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100'
                            : 'bg-red-100 text-red-800 dark:bg-red-800 dark:text-red-100'
                        }`}
                      >
                        {user.status === 'activo' ? 'Activo' : 'Inactivo'}
                      </span>
                    </div>
                    <p className="text-gray-600 dark:text-gray-300 mb-2">
                      {user.email}
                    </p>
                    <div className="flex items-center gap-4 text-sm text-gray-500 dark:text-gray-400">
                      <span>Rol: {user.role}</span>
                      <span>Llaves: {user.keys?.length || 0}</span>
                      {user.lastLogin && (
                        <span>Último acceso: {new Date(user.lastLogin).toLocaleDateString()}</span>
                      )}
                    </div>
                  </div>
                  
                  <div className="flex gap-2">
                    <Button
                      onClick={() => handleViewUserKeys(user)}
                      className="p-2 text-blue-600 hover:bg-blue-50 dark:hover:bg-blue-900 rounded-lg transition-colors"
                      title="Ver llaves"
                    >
                      <Key size={16} />
                    </Button>
                    <Button
                      onClick={() => handleEditUser(user)}
                      className="p-2 text-gray-600 hover:bg-gray-50 dark:hover:bg-gray-700 rounded-lg transition-colors"
                      title="Editar usuario"
                    >
                      <Edit size={16} />
                    </Button>
                    <Button
                      onClick={() => handleDeleteUser(user)}
                      className="p-2 text-red-600 hover:bg-red-50 dark:hover:bg-red-900 rounded-lg transition-colors"
                      title="Eliminar usuario"
                    >
                      <Trash2 size={16} />
                    </Button>
                  </div>
                </div>
              </div>
            ))
          )}
        </div>
      )}

      {/* Modales */}
      <UserCreateModal
        isOpen={showCreateModal}
        onClose={() => setShowCreateModal(false)}
        onCreate={handleCreateUser}
        darkMode={darkMode}
      />

      <UserEditModal
        isOpen={showEditModal}
        user={selectedUser}
        onClose={() => {
          setShowEditModal(false);
          setSelectedUser(null);
        }}
        onSave={handleUpdateUser}
        darkMode={darkMode}
      />

      <UserDeleteModal
        isOpen={showDeleteModal}
        user={selectedUser}
        onClose={() => {
          setShowDeleteModal(false);
          setSelectedUser(null);
        }}
        onConfirm={handleConfirmDelete}
        darkMode={darkMode}
      />

      <UserKeysModal
        isOpen={showKeysModal}
        user={selectedUser}
        userKeys={userKeys}
        isLoading={keysLoading}
        onClose={handleCloseKeysModal}
        darkMode={darkMode}
      />
    </div>
  );
};

UserManagement.propTypes = {
  users: PropTypes.array.isRequired,
  usersWithKeys: PropTypes.array,
  isLoading: PropTypes.bool,
  error: PropTypes.string,
  onClearError: PropTypes.func.isRequired,
  onUpdateUser: PropTypes.func.isRequired,
  onCreateUser: PropTypes.func.isRequired,
  onDeleteUser: PropTypes.func.isRequired,
  onGetUserKeys: PropTypes.func.isRequired,
  darkMode: PropTypes.bool
};

UserManagement.defaultProps = {
  usersWithKeys: [],
  isLoading: false,
  error: null,
  darkMode: false
};

export default UserManagement;
