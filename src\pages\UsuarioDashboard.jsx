import { useEffect, useRef, useState } from "react";
import gsap from "gsap";
import {
  Sun,
  Moon,
  Key,
  User,
  BarChart3,
  Plus,
  Edit,
  Trash2,
  X,
  Save,
  Mail,
  Calendar,
  Shield,
} from "lucide-react";
import secureStorage from '../utils/SecureStorage';
import securityMonitor from '../utils/SecurityMonitor';
import { useAuth, useKeys, useUsers } from '../hooks/index';
import { DashboardLayout, DashboardStats } from '../components/dashboard';
import { KeyManagement, ProfileManagement } from '../components/dashboard/user';
import { useLanguage } from '../i18n';

const UsuarioDashboard = () => {
  const sidebarRef = useRef(null);
  const mainContentRef = useRef(null);
  const [darkMode, setDarkMode] = useState(false);
  const [activeSection, setActiveSection] = useState('dashboard');
  const [showEditProfile, setShowEditProfile] = useState(false);
  const [showGenerateKey, setShowGenerateKey] = useState(false);
  const [showLogoutConfirm, setShowLogoutConfirm] = useState(false);
  const [userProfile, setUserProfile] = useState({
    name: "Juan Pérez",
    email: "<EMAIL>",
    joinDate: "2024-01-15",
    lastLogin: new Date().toISOString()
  });

  // Hooks para autenticación, llaves y usuarios
  const { user: currentUser, logout } = useAuth();
  const { t } = useLanguage();
  const {
    keys: userKeys,
    statistics,
    isLoading: keysLoading,
    error: keysError,
    uploadToCTM,
    getKeysByUser,
    deleteKey,
    clearError
  } = useKeys();
  const {
    updateUser,
    isLoading: userLoading,
    error: userError,
    clearError: clearUserError
  } = useUsers();

  // 🔁 Animaciones GSAP al montar
  useEffect(() => {
    if (window.innerWidth >= 768) {
      gsap.fromTo(
        sidebarRef.current,
        { x: -100, opacity: 0 },
        { x: 0, opacity: 1, duration: 1, ease: "power2.out" }
      );
    }

    gsap.fromTo(
      mainContentRef.current,
      { y: 100, opacity: 0 },
      { y: 0, opacity: 1, duration: 1, ease: "power2.out", delay: 0.3 }
    );
  }, []);

  // 🛡️ Inicializar monitoreo de seguridad para usuario
  useEffect(() => {
    securityMonitor.start();

    console.log('[USER_SECURITY] User dashboard accessed at:', new Date().toISOString());
    console.log('[USER_SECURITY] User info:', userProfile);

    // Verificar integridad de datos de usuario
    const userRole = secureStorage.getSecure('user_role') || sessionStorage.getItem('userRole');
    if (userRole !== 'usuario') {
      console.error('[USER_SECURITY] Invalid user role detected:', userRole);
      handleLogout();
    }

    return () => {
      securityMonitor.stop();
    };
  }, []);

  // Cargar llaves del usuario
  useEffect(() => {
    const loadUserKeys = async () => {
      if (currentUser && currentUser.id) {
        try {
          await getKeysByUser(currentUser.id);
        } catch {
          // Error loading user keys - handled by hooks
        }
      }
    };

    loadUserKeys();
  }, [currentUser, getKeysByUser]);

  // 🔁 Toggle de modo oscuro
  const toggleDarkMode = () => {
    const newDarkMode = !darkMode;
    setDarkMode(newDarkMode);

    // Agregar/quitar clase 'dark' del HTML para que Tailwind funcione
    if (newDarkMode) {
      document.documentElement.classList.add('dark');
    } else {
      document.documentElement.classList.remove('dark');
    }
  };

  // 🔁 Cerrar sesión seguro
  const handleLogout = () => {
    console.log('[USER_SECURITY] User logout initiated at:', new Date().toISOString());

    securityMonitor.stop();
    secureStorage.clearAllSecure();

    sessionStorage.removeItem('token');
    sessionStorage.removeItem('userRole');
    sessionStorage.removeItem('tokenExpiry');
    sessionStorage.removeItem('loginTime');
    localStorage.removeItem('token');
    localStorage.removeItem('userRole');

    sessionStorage.setItem('wasLoggedOut', 'true');

    console.log('[USER_SECURITY] User logout completed at:', new Date().toISOString());

    logout(); // Usar el hook de logout
  };

  // 🔑 Generar nueva llave cuántica
  const generateNewKey = () => {
    const newKey = {
      id: `qkey_${Date.now()}_jp`,
      name: `Llave ${userKeys.length + 1}`,
      created: new Date().toISOString().split('T')[0],
      lastUsed: "Nunca",
      status: "activa",
      type: "QRNG-256",
      uses: 0
    };

    setUserKeys([...userKeys, newKey]);
    setShowGenerateKey(false);

    console.log('[USER_SECURITY] New quantum key generated:', newKey.id);
  };

  // 👤 Actualizar perfil de usuario
  const updateProfile = (updatedData) => {
    setUserProfile({ ...userProfile, ...updatedData });
    setShowEditProfile(false);

    console.log('[USER_SECURITY] Profile updated:', updatedData);
  };



  // 🔑 Manejar subida de llave
  const handleUploadKey = async (keyData) => {
    try {
      await uploadToCTM(keyData);
    } catch (error) {
      console.error('[USER_SECURITY] Error uploading key:', error);
    }
  };

  // 🗑️ Manejar eliminación de llave
  const handleDeleteKey = async (keyId) => {
    try {
      await deleteKey(keyId);
    } catch (error) {
      console.error('[USER_SECURITY] Error deleting key:', error);
    }
  };

  // 👤 Manejar actualización de perfil
  const handleUpdateProfile = async (profileData) => {
    try {
      await updateUser(currentUser.id, profileData);
    } catch (error) {
      console.error('[USER_SECURITY] Error updating profile:', error);
    }
  };

  // 🔒 Manejar cambio de contraseña
  const handleChangePassword = async (passwordData) => {
    try {
      await updateUser(currentUser.id, { password: passwordData.newPassword });
    } catch (error) {
      console.error('[USER_SECURITY] Error changing password:', error);
    }
  };

  // Configuración de navegación
  const navigationItems = [
    { key: 'dashboard', label: t('navigation.dashboard'), icon: BarChart3 },
    { key: 'keys', label: t('navigation.keys'), icon: Key },
    { key: 'profile', label: t('navigation.profile'), icon: User }
  ];

  // Configuración de estadísticas principales
  const mainStats = [
    {
      icon: Key,
      title: t('dashboard.myKeys'),
      value: statistics?.total || userKeys.length,
      description: t('dashboard.generatedKeys'),
      iconColor: "text-blue-500",
      valueColor: "text-blue-600 dark:text-blue-400"
    },
    {
      icon: Shield,
      title: t('dashboard.activeKeys'),
      value: statistics?.uploadedToCtm || userKeys.filter(key =>
        key.status === 'UPLOADED_TO_CTM' || key.uploadedToCtm === true
      ).length,
      description: t('dashboard.workingKeys'),
      iconColor: "text-green-500",
      valueColor: "text-green-600 dark:text-green-400"
    },
    {
      icon: Calendar,
      title: t('dashboard.lastLogin'),
      value: new Date().toLocaleDateString('es-ES', {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric'
      }),
      description: t('dashboard.lastLoginDate'),
      iconColor: "text-purple-500",
      valueColor: "text-purple-600 dark:text-purple-400"
    }
  ];

  // Configuración de estadísticas adicionales
  const miniStats = statistics ? [
    {
      value: statistics.successful,
      label: t('dashboard.successful'),
      valueColor: "text-green-600 dark:text-green-400"
    },
    {
      value: statistics.failed,
      label: t('dashboard.failed'),
      valueColor: "text-red-600 dark:text-red-400"
    },
    {
      value: statistics.uploadedToCtm,
      label: t('dashboard.inCTM'),
      valueColor: "text-blue-600 dark:text-blue-400"
    },
    {
      value: (() => {
        if (!statistics.total || statistics.total === 0) return 0;
        const percentage = Math.round((statistics.successful / statistics.total) * 100);
        return Math.min(percentage, 100); // Asegurar que no exceda 100%
      })(),
      label: t('dashboard.successRate'),
      valueColor: "text-purple-600 dark:text-purple-400"
    }
  ] : [];

  const renderContent = (darkMode = false) => {
    switch (activeSection) {
      case 'dashboard':
        return (
          <>
            <h1 className="text-3xl font-light tracking-wide leading-relaxed mb-4 text-gray-900 dark:text-white">
              {t('dashboard.userWelcome')}, {currentUser ? `${currentUser.firstName} ${currentUser.lastName}` : t('auth.welcome')}
            </h1>
            <p className="text-gray-600 dark:text-gray-300 mb-6 font-light tracking-wide">
              {t('dashboard.userSubtitle')}
            </p>

            <DashboardStats
              mainStats={mainStats}
              miniStats={miniStats}
              isLoading={keysLoading}
              error={keysError}
              onClearError={clearError}
            />
          </>
        );

      case 'keys':
        return (
          <KeyManagement
            keys={userKeys}
            isLoading={keysLoading}
            error={keysError}
            onClearError={clearError}
            onUploadKey={handleUploadKey}
            onDeleteKey={handleDeleteKey}
            darkMode={darkMode}
          />
        );

      case 'profile':
        return (
          <ProfileManagement
            currentUser={currentUser}
            isLoading={userLoading}
            error={userError}
            onClearError={clearUserError}
            onUpdateProfile={handleUpdateProfile}
            onChangePassword={handleChangePassword}
            darkMode={darkMode}
          />
        );

      default:
        return null;
    }
  };

  // Componente wrapper que recibe darkMode del DashboardLayout
  const ContentWrapper = ({ darkMode }) => {
    return renderContent(darkMode);
  };
  return (
    <div
      className={`flex flex-col lg:flex-row min-h-screen transition-colors duration-500 ${
        darkMode ? "bg-gray-700 text-white" : "bg-gray-100 text-gray-900"
      }`}
    >
      {/* Sidebar responsivo */}
      <aside
        ref={sidebarRef}
        className={`w-full lg:w-64 xl:w-72 lg:h-screen shadow-xl z-10 p-4 lg:p-6 flex flex-col justify-between transition-colors duration-500
        ${darkMode ? "bg-gray-800 text-white" : "bg-white text-gray-800"}`}
      >
        {/* Información del Usuario */}
        <div>
          <div className="flex justify-center items-center gap-3 mb-4">
            <img
              src="/src/assets/logo-usuario.png"
              alt="Quantum Logo"
              className="w-10 h-10"
            />
            <img
              src={darkMode ? "/src/assets/secure-logo-blanco.png" : "/src/assets/sequre-logo-negro.svg"}
              alt="Segundo Logo"
              className="w-100 h-10"
            />
          </div>

          {/* Información del usuario */}
          <div className={`mb-8 p-4 rounded-xl border shadow-lg hover:shadow-xl transition-all duration-300 ${darkMode ? 'bg-gray-700 border-gray-600' : 'bg-gray-50 border-gray-200'}`}>
            <div className="flex items-center gap-3 mb-3">
              <User size={16} className="text-blue-500" />
              <h3 className={`font-light tracking-wide leading-relaxed text-sm ${darkMode ? 'text-white' : 'text-gray-800'}`}>
                <span className={`font-medium ${darkMode ? 'text-white' : 'text-gray-900'}`}>{userProfile.name}</span>
              </h3>
            </div>
            <div className="flex items-center gap-3">
              <Mail size={16} className="text-green-500" />
              <p className={`text-xs font-light tracking-wide ${darkMode ? 'text-gray-300' : 'text-gray-600'}`}>
                {userProfile.email}
              </p>
            </div>
          </div>

          <nav className="space-y-4">
            <button
              onClick={() => setActiveSection('dashboard')}
              className={`flex items-center gap-3 px-4 py-3 rounded-xl transition-all duration-300 w-full text-left hover:shadow-lg ${
                activeSection === 'dashboard'
                  ? (darkMode ? 'bg-blue-900 text-blue-300 shadow-lg' : 'bg-blue-100 text-blue-700 shadow-lg')
                  : (darkMode ? 'hover:bg-gray-700' : 'hover:bg-gray-100')
              }`}
            >
              <BarChart3 size={18} />
              <span className={`font-light tracking-wide ${darkMode ? 'text-white' : 'text-gray-800'}`}>
                Dashboard
              </span>
            </button>
            <button
              onClick={() => setActiveSection('keys')}
              className={`flex items-center gap-3 px-4 py-3 rounded-xl transition-all duration-300 w-full text-left hover:shadow-lg ${
                activeSection === 'keys'
                  ? (darkMode ? 'bg-blue-900 text-blue-300 shadow-lg' : 'bg-blue-100 text-blue-700 shadow-lg')
                  : (darkMode ? 'hover:bg-gray-700' : 'hover:bg-gray-100')
              }`}
            >
              <Key size={18} />
              <span className={`font-light tracking-wide ${darkMode ? 'text-white' : 'text-gray-800'}`}>
                Mis Llaves
              </span>
            </button>
            <button
              onClick={() => setActiveSection('profile')}
              className={`flex items-center gap-3 px-4 py-3 rounded-xl transition-all duration-300 w-full text-left hover:shadow-lg ${
                activeSection === 'profile'
                  ? (darkMode ? 'bg-blue-900 text-blue-300 shadow-lg' : 'bg-blue-100 text-blue-700 shadow-lg')
                  : (darkMode ? 'hover:bg-gray-700' : 'hover:bg-gray-100')
              }`}
            >
              <User size={18} />
              <span className={`font-light tracking-wide ${darkMode ? 'text-white' : 'text-gray-800'}`}>
                Mi Perfil
              </span>
            </button>

          </nav>
        </div>

        {/* Botones: modo claro/oscuro + logout */}
        <div className="flex flex-col gap-4">
          <button
            onClick={toggleDarkMode}
            className={`flex items-center justify-center gap-3 w-full px-4 py-3 rounded-xl shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300 group
              ${darkMode
                ? "bg-gradient-to-r from-yellow-600 to-yellow-700 hover:from-yellow-700 hover:to-yellow-800 text-white border border-yellow-500"
                : "bg-gradient-to-r from-gray-600 to-gray-700 hover:from-gray-700 hover:to-gray-800 text-white border border-gray-500"}`}
          >
            {darkMode ? (
              <>
                <div className="p-1 bg-white/20 rounded-lg group-hover:bg-white/30 transition-all duration-300">
                  <Sun size={18} className="text-yellow-300 animate-pulse group-hover:rotate-180 transition-transform duration-500" />
                </div>
                <span className="font-light tracking-wide">Modo Claro</span>
              </>
            ) : (
              <>
                <div className="p-1 bg-white/20 rounded-lg group-hover:bg-white/30 transition-all duration-300">
                  <Moon size={18} className="text-indigo-300 animate-pulse group-hover:rotate-12 transition-transform duration-300" />
                </div>
                <span className="font-light tracking-wide">Modo Oscuro</span>
              </>
            )}
          </button>

          <button
            onClick={() => setShowLogoutConfirm(true)}
            className="flex items-center justify-center gap-3 w-full px-4 py-3 rounded-xl bg-gradient-to-r from-red-600 to-red-700 hover:from-red-700 hover:to-red-800 text-white shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300 group"
          >
            <div className="p-1 bg-white/20 rounded-lg group-hover:bg-white/30 transition-all duration-300">
              <X size={18} className="group-hover:rotate-90 transition-transform duration-300" />
            </div>
            <span className="font-light tracking-wide">Cerrar Sesión</span>
          </button>
        </div>
      </aside>

      {/* Contenido Principal */}
      <main
        ref={mainContentRef}
        className={`flex-1 p-4 sm:p-6 lg:p-10 overflow-y-auto relative transition-colors duration-500
          ${darkMode ? "bg-gray-700" : "bg-gray-50"}`}
      >
        {/* Contenedor principal */}
        <div
          className={`relative z-10 transition-all duration-500 rounded-xl p-4 sm:p-6 lg:p-8 shadow-md border
            ${darkMode
              ? "bg-gray-800 border-gray-700 text-white"
              : "bg-white border-gray-200 text-gray-900"}`}
        >
          {/* Dashboard Principal */}
          {activeSection === 'dashboard' && (
            <>
              <h1 className={`text-4xl font-black tracking-wider uppercase mb-4 ${darkMode ? 'text-white' : 'text-gray-700'}`}>
                BIENVENIDO, {userProfile.name.toUpperCase()}
              </h1>
              <p className={`text-lg font-light tracking-wide leading-relaxed mb-6 ${darkMode ? 'text-gray-200' : 'text-gray-700'}`}>
                <span className={`font-medium ${darkMode ? 'text-white' : 'text-gray-800'}`}>Sistema QRNG Quantum</span> •
                Gestiona tus llaves cuánticas y actualiza tu información personal con tecnología de vanguardia.
              </p>

              {/* Tarjetas de información */}
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
                <div className={`p-6 rounded-xl border shadow-lg hover:shadow-2xl hover:-translate-y-1 hover:scale-[1.02] transition-all duration-300 group cursor-pointer ${darkMode ? 'bg-gray-700 border-gray-600' : 'bg-white border-gray-200'}`}>
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center gap-3">
                      <div className="p-2 bg-blue-100 dark:bg-blue-900/30 rounded-lg group-hover:scale-125 group-hover:rotate-12 transition-all duration-500">
                        <Key size={20} className="text-blue-500 group-hover:text-blue-600" />
                      </div>
                      <span className={`font-semibold group-hover:text-blue-600 transition-colors duration-300 ${darkMode ? 'text-white' : 'text-gray-900'}`}>Mis Llaves</span>
                    </div>
                  </div>

                  <div className="flex items-end justify-between">
                    <div>
                      <p className="text-3xl font-bold text-blue-600 dark:text-blue-400 mb-1 group-hover:scale-110 transition-transform duration-300">
                        {userKeys.length}
                      </p>
                      <p className={`text-sm ${darkMode ? 'text-gray-300' : 'text-gray-600'}`}>
                        Llaves cuánticas generadas
                      </p>
                    </div>
                  </div>
                </div>

                <div className={`p-6 rounded-xl border shadow-lg hover:shadow-2xl hover:-translate-y-1 hover:scale-[1.02] transition-all duration-300 group cursor-pointer ${darkMode ? 'bg-gray-700 border-gray-600' : 'bg-white border-gray-200'}`}>
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center gap-3">
                      <div className="p-2 bg-green-100 dark:bg-green-900/30 rounded-lg group-hover:scale-125 group-hover:rotate-12 transition-all duration-500">
                        <Shield size={20} className="text-green-500 group-hover:text-green-600" />
                      </div>
                      <span className={`font-semibold group-hover:text-green-600 transition-colors duration-300 ${darkMode ? 'text-white' : 'text-gray-900'}`}>Llaves Activas</span>
                    </div>
                  </div>

                  <div className="flex items-end justify-between">
                    <div>
                      <p className="text-3xl font-bold text-green-600 dark:text-green-400 mb-1 group-hover:scale-110 transition-transform duration-300">
                        {userKeys.filter(key => key.status === 'activa').length}
                      </p>
                      <p className={`text-sm ${darkMode ? 'text-gray-300' : 'text-gray-600'}`}>
                        Llaves en funcionamiento
                      </p>
                    </div>
                    <div className="relative w-16 h-16 group-hover:scale-110 transition-transform duration-300">
                      <svg className="w-16 h-16 transform -rotate-90" viewBox="0 0 36 36">
                        <path
                          d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
                          fill="none"
                          stroke={darkMode ? "#374151" : "#e5e7eb"}
                          strokeWidth="3"
                        />
                        <path
                          d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
                          fill="none"
                          stroke="#10b981"
                          strokeWidth="3"
                          strokeDasharray={`${(userKeys.filter(key => key.status === 'activa').length / userKeys.length) * 100}, 100`}
                          className="transition-all duration-1000 delay-300 group-hover:stroke-green-600"
                        />
                      </svg>
                      <div className="absolute inset-0 flex items-center justify-center">
                        <span className="text-xs font-bold text-green-600 dark:text-green-400">
                          {userKeys.length > 0 ? Math.round((userKeys.filter(key => key.status === 'activa').length / userKeys.length) * 100) : 0}%
                        </span>
                      </div>
                    </div>
                  </div>
                </div>

                <div className={`p-6 rounded-xl border shadow-lg hover:shadow-2xl hover:-translate-y-1 hover:scale-[1.02] transition-all duration-300 group cursor-pointer ${darkMode ? 'bg-gray-700 border-gray-600' : 'bg-white border-gray-200'}`}>
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center gap-3">
                      <div className="p-2 bg-purple-100 dark:bg-purple-900/30 rounded-lg group-hover:scale-125 group-hover:rotate-12 transition-all duration-500">
                        <Calendar size={20} className="text-purple-500 group-hover:text-purple-600" />
                      </div>
                      <span className={`font-semibold group-hover:text-purple-600 transition-colors duration-300 ${darkMode ? 'text-white' : 'text-gray-900'}`}>Último Acceso</span>
                    </div>
                  </div>

                  <div className="flex items-end justify-between">
                    <div>
                      <p className="text-2xl font-bold text-purple-600 dark:text-purple-400 mb-1 group-hover:scale-110 transition-transform duration-300">
                        {new Date().toLocaleDateString()}
                      </p>
                      <p className={`text-sm ${darkMode ? 'text-gray-300' : 'text-gray-600'}`}>
                        Fecha de último login
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </>
          )}

          {/* Sección de Llaves */}
          {activeSection === 'keys' && (
            <>
              <div className="mb-8">
                <h1 className={`text-lg font-light tracking-wide leading-relaxed mb-6 ${darkMode ? 'text-white' : 'text-gray-700'}`}>
                  <span className={`font-medium ${darkMode ? 'text-white' : 'text-gray-800'}`}>Mis Llaves Cuánticas</span> •
                  Gestiona y supervisa todas tus llaves API del sistema QRNG Quantum.
                </h1>
                <div className="flex justify-center">
                  <button
                    onClick={() => setShowGenerateKey(true)}
                    className="flex items-center justify-center gap-3 bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white px-8 py-3 rounded-xl shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300 text-sm font-semibold tracking-wide min-w-[200px] group"
                  >
                    <div className="p-1 bg-white/20 rounded-lg group-hover:bg-white/30 transition-all duration-300">
                      <Plus size={18} className="group-hover:rotate-90 transition-transform duration-300" />
                    </div>
                    Generar Nueva Llave
                  </button>
                </div>
              </div>

              {/* Lista de llaves */}
              <div className="space-y-4">
                {userKeys.map((key) => (
                  <div
                    key={key.id}
                    className={`p-6 rounded-xl border shadow-lg hover:shadow-2xl hover:-translate-y-1 hover:scale-[1.02] transition-all duration-300 group cursor-pointer ${
                      darkMode ? 'bg-gray-700 border-gray-600' : 'bg-white border-gray-200'
                    }`}
                  >
                    <div className="flex justify-between items-start">
                      <div className="flex-1">
                        <div className="flex items-center gap-4 mb-3">
                          <h3 className="text-xl font-semibold">{key.name}</h3>
                          <span
                            className={`inline-flex items-center px-4 py-2 rounded-xl text-sm font-medium tracking-wide transition-all duration-200 group-hover:scale-105 ${
                              key.status === 'activa'
                                ? 'bg-green-50 text-green-700 border border-green-200 shadow-sm'
                                : 'bg-red-50 text-red-700 border border-red-200 shadow-sm'
                            }`}
                          >
                            <div className={`w-2 h-2 rounded-full mr-2 ${
                              key.status === 'activa' ? 'bg-green-500' : 'bg-red-500'
                            }`}></div>
                            {key.status === 'activa' ? 'Activa' : 'Inactiva'}
                          </span>
                          <span className="px-2 py-1 rounded text-xs bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
                            {key.type}
                          </span>
                        </div>

                        <div className="mb-4">
                          <p className="font-mono text-sm bg-gray-100 dark:bg-gray-600 p-2 rounded">
                            {key.id}
                          </p>
                        </div>

                        <div className="grid grid-cols-1 sm:grid-cols-3 gap-4 text-sm">
                          <div>
                            <span className="text-gray-600 dark:text-gray-300">Creada:</span>
                            <p className="font-medium">{key.created}</p>
                          </div>
                          <div>
                            <span className="text-gray-600 dark:text-gray-300">Último uso:</span>
                            <p className="font-medium">{key.lastUsed}</p>
                          </div>
                          <div>
                            <span className="text-gray-600 dark:text-gray-300">Usos:</span>
                            <p className="font-medium">{key.uses}</p>
                          </div>
                        </div>
                      </div>

                      {/* Botón de eliminar */}
                      <div className="ml-4">
                        <button
                          onClick={() => deleteKey(key.id)}
                          className="p-2 rounded-lg bg-red-100 hover:bg-red-200 text-red-700 dark:bg-red-900 dark:hover:bg-red-800 dark:text-red-300 transition"
                          title="Eliminar llave"
                        >
                          <Trash2 size={16} />
                        </button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </>
          )}

          {/* Sección de Perfil */}
          {activeSection === 'profile' && (
            <>
              <div className="mb-8">
                <h1 className={`text-lg font-light tracking-wide leading-relaxed mb-6 ${darkMode ? 'text-white' : 'text-gray-700'}`}>
                  <span className={`font-medium ${darkMode ? 'text-white' : 'text-gray-800'}`}>Mi Perfil</span> •
                  Gestiona y actualiza tu información personal del sistema QRNG Quantum.
                </h1>
                <div className="flex justify-center">
                  <button
                    onClick={() => setShowEditProfile(true)}
                    className="flex items-center justify-center gap-3 bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 text-white px-8 py-3 rounded-xl shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300 text-sm font-semibold tracking-wide min-w-[180px] group"
                  >
                    <div className="p-1 bg-white/20 rounded-lg group-hover:bg-white/30 transition-all duration-300">
                      <Edit size={18} className="group-hover:rotate-12 transition-transform duration-300" />
                    </div>
                    Editar Perfil
                  </button>
                </div>
              </div>

              {/* Información del perfil */}
              <div className={`p-6 rounded-xl border shadow-lg hover:shadow-2xl hover:-translate-y-1 hover:scale-[1.02] transition-all duration-300 group cursor-pointer ${
                darkMode ? 'bg-gray-700 border-gray-600' : 'bg-white border-gray-200'
              }`}>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className={`block text-sm font-medium mb-1 ${darkMode ? 'text-gray-300' : 'text-gray-600'}`}>Nombre Completo</label>
                    <p className={`text-lg font-semibold ${darkMode ? 'text-white' : 'text-gray-900'}`}>{userProfile.name}</p>
                  </div>
                  <div>
                    <label className={`block text-sm font-medium mb-1 ${darkMode ? 'text-gray-300' : 'text-gray-600'}`}>Email</label>
                    <p className={`text-lg font-semibold ${darkMode ? 'text-white' : 'text-gray-900'}`}>{userProfile.email}</p>
                  </div>
                  <div>
                    <label className={`block text-sm font-medium mb-1 ${darkMode ? 'text-gray-300' : 'text-gray-600'}`}>Fecha de Registro</label>
                    <p className={`text-lg font-semibold ${darkMode ? 'text-white' : 'text-gray-900'}`}>{userProfile.joinDate}</p>
                  </div>
                  <div>
                    <label className={`block text-sm font-medium mb-1 ${darkMode ? 'text-gray-300' : 'text-gray-600'}`}>Último Login</label>
                    <p className={`text-lg font-semibold ${darkMode ? 'text-white' : 'text-gray-900'}`}>{new Date(userProfile.lastLogin).toLocaleString()}</p>
                  </div>
                </div>
              </div>
            </>
          )}


        </div>
      </main>

      {/* Modal para Generar Nueva Llave */}
      {showGenerateKey && (
        <div className="fixed inset-0 bg-black/60 backdrop-blur-sm flex items-center justify-center z-50 animate-fadeIn">
          <div className={`p-4 sm:p-8 rounded-2xl shadow-2xl max-w-sm sm:max-w-lg w-full mx-4 transform animate-slideUp border ${
            darkMode ? 'bg-gray-800 text-white border-gray-600' : 'bg-white text-gray-900 border-gray-200'
          }`}>
            {/* Header del Modal */}
            <div className="flex justify-between items-start mb-6">
              <div>
                <h3 className={`text-xl font-light tracking-wide leading-relaxed ${darkMode ? 'text-white' : 'text-gray-800'}`}>
                  <span className={`font-medium ${darkMode ? 'text-white' : 'text-gray-900'}`}>Generar Nueva Llave</span> •
                  <span className="text-lg"> Cuántica QRNG</span>
                </h3>
              </div>
              <button
                onClick={() => setShowGenerateKey(false)}
                className={`p-2 rounded-xl transition-all duration-300 hover:scale-110 group ${
                  darkMode ? 'hover:bg-gray-700' : 'hover:bg-gray-100'
                }`}
              >
                <X size={20} className="group-hover:rotate-90 transition-transform duration-300" />
              </button>
            </div>

            {/* Contenido del Modal */}
            <div className={`p-6 rounded-xl border mb-6 ${
              darkMode ? 'bg-gray-700/50 border-gray-600' : 'bg-gray-50 border-gray-200'
            }`}>
              <div className="flex items-center gap-3 mb-3">
                <div className="p-2 bg-blue-100 dark:bg-blue-900/30 rounded-lg">
                  <Key size={20} className="text-blue-500" />
                </div>
                <span className={`font-medium ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                  Nueva Llave QRNG-256
                </span>
              </div>
              <p className={`text-sm font-light tracking-wide ${darkMode ? 'text-gray-300' : 'text-gray-600'}`}>
                Se generará una nueva llave cuántica de alta seguridad para tu cuenta con tecnología QRNG-256.
              </p>
            </div>

            {/* Botones del Modal */}
            <div className="flex flex-col sm:flex-row gap-4">
              <button
                onClick={generateNewKey}
                className="flex-1 flex items-center justify-center gap-3 bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white px-6 py-3 rounded-xl shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300 group"
              >
                <div className="p-1 bg-white/20 rounded-lg group-hover:bg-white/30 transition-all duration-300">
                  <Plus size={18} className="group-hover:rotate-90 transition-transform duration-300" />
                </div>
                <span className="font-light tracking-wide">Generar Llave</span>
              </button>
              <button
                onClick={() => setShowGenerateKey(false)}
                className={`flex-1 flex items-center justify-center gap-3 px-6 py-3 rounded-xl shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300 group ${
                  darkMode
                    ? 'bg-gradient-to-r from-gray-700 to-gray-800 hover:from-gray-800 hover:to-gray-900 text-white'
                    : 'bg-gradient-to-r from-gray-200 to-gray-300 hover:from-gray-300 hover:to-gray-400 text-gray-900'
                }`}
              >
                <div className={`p-1 rounded-lg group-hover:bg-white/30 transition-all duration-300 ${
                  darkMode ? 'bg-white/20' : 'bg-black/10'
                }`}>
                  <X size={18} className="group-hover:rotate-90 transition-transform duration-300" />
                </div>
                <span className="font-light tracking-wide">Cancelar</span>
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Modal para Editar Perfil */}
      {showEditProfile && (
        <div className="fixed inset-0 bg-black/60 backdrop-blur-sm flex items-center justify-center z-50 animate-fadeIn">
          <div className={`p-4 sm:p-8 rounded-2xl shadow-2xl max-w-sm sm:max-w-lg w-full mx-4 transform animate-slideUp border ${
            darkMode ? 'bg-gray-800 text-white border-gray-600' : 'bg-white text-gray-900 border-gray-200'
          }`}>
            {/* Header del Modal */}
            <div className="flex justify-between items-start mb-6">
              <div>
                <h3 className={`text-xl font-light tracking-wide leading-relaxed ${darkMode ? 'text-white' : 'text-gray-800'}`}>
                  <span className={`font-medium ${darkMode ? 'text-white' : 'text-gray-900'}`}>Editar Perfil</span> •
                  <span className="text-lg"> Información Personal</span>
                </h3>
              </div>
              <button
                onClick={() => setShowEditProfile(false)}
                className={`p-2 rounded-xl transition-all duration-300 hover:scale-110 group ${
                  darkMode ? 'hover:bg-gray-700' : 'hover:bg-gray-100'
                }`}
              >
                <X size={20} className="group-hover:rotate-90 transition-transform duration-300" />
              </button>
            </div>

            {/* Contenido del Modal */}
            <div className={`p-6 rounded-xl border mb-6 ${
              darkMode ? 'bg-gray-700/50 border-gray-600' : 'bg-gray-50 border-gray-200'
            }`}>
              <div className="flex items-center gap-3 mb-4">
                <div className="p-2 bg-green-100 dark:bg-green-900/30 rounded-lg">
                  <User size={20} className="text-green-500" />
                </div>
                <span className={`font-medium ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                  Actualizar Datos
                </span>
              </div>

              <div className="space-y-4">
                <div>
                  <label className={`block text-sm font-medium mb-2 ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                    Nombre Completo
                  </label>
                  <input
                    type="text"
                    defaultValue={userProfile.name}
                    className={`w-full px-4 py-3 border rounded-xl transition-all duration-300 focus:ring-2 focus:ring-green-500 focus:border-green-500 ${
                      darkMode
                        ? 'bg-gray-700 border-gray-600 text-white placeholder-gray-400'
                        : 'bg-white border-gray-300 text-gray-900 placeholder-gray-500'
                    }`}
                    id="editName"
                    placeholder="Ingresa tu nombre completo"
                  />
                </div>

                <div>
                  <label className={`block text-sm font-medium mb-2 ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                    Correo Electrónico
                  </label>
                  <input
                    type="email"
                    defaultValue={userProfile.email}
                    className={`w-full px-4 py-3 border rounded-xl transition-all duration-300 focus:ring-2 focus:ring-green-500 focus:border-green-500 ${
                      darkMode
                        ? 'bg-gray-700 border-gray-600 text-white placeholder-gray-400'
                        : 'bg-white border-gray-300 text-gray-900 placeholder-gray-500'
                    }`}
                    id="editEmail"
                    placeholder="Ingresa tu correo electrónico"
                  />
                </div>
              </div>
            </div>

            {/* Botones del Modal */}
            <div className="flex flex-col sm:flex-row gap-4">
              <button
                onClick={() => {
                  const name = document.getElementById('editName').value;
                  const email = document.getElementById('editEmail').value;
                  updateProfile({ name, email });
                }}
                className="flex-1 flex items-center justify-center gap-3 bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 text-white px-6 py-3 rounded-xl shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300 group"
              >
                <div className="p-1 bg-white/20 rounded-lg group-hover:bg-white/30 transition-all duration-300">
                  <Save size={18} className="group-hover:scale-110 transition-transform duration-300" />
                </div>
                <span className="font-light tracking-wide">Guardar Cambios</span>
              </button>
              <button
                onClick={() => setShowEditProfile(false)}
                className={`flex-1 flex items-center justify-center gap-3 px-6 py-3 rounded-xl shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300 group ${
                  darkMode
                    ? 'bg-gradient-to-r from-gray-700 to-gray-800 hover:from-gray-800 hover:to-gray-900 text-white'
                    : 'bg-gradient-to-r from-gray-200 to-gray-300 hover:from-gray-300 hover:to-gray-400 text-gray-900'
                }`}
              >
                <div className={`p-1 rounded-lg group-hover:bg-white/30 transition-all duration-300 ${
                  darkMode ? 'bg-white/20' : 'bg-black/10'
                }`}>
                  <X size={18} className="group-hover:rotate-90 transition-transform duration-300" />
                </div>
                <span className="font-light tracking-wide">Cancelar</span>
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Modal de Confirmación de Logout */}
      {showLogoutConfirm && (
        <div className="fixed inset-0 bg-black/60 backdrop-blur-sm flex items-center justify-center z-50 animate-fadeIn">
          <div className={`p-4 sm:p-8 rounded-2xl shadow-2xl max-w-xs sm:max-w-md w-full mx-4 transform animate-slideUp border ${
            darkMode ? 'bg-gray-800 text-white border-gray-600' : 'bg-white text-gray-900 border-gray-200'
          }`}>
            {/* Header del Modal */}
            <div className="flex justify-between items-start mb-6">
              <div>
                <h3 className={`text-xl font-light tracking-wide leading-relaxed ${darkMode ? 'text-white' : 'text-gray-800'}`}>
                  <span className={`font-medium ${darkMode ? 'text-white' : 'text-gray-900'}`}>Cerrar Sesión</span> •
                  <span className="text-lg"> Confirmación</span>
                </h3>
              </div>
              <button
                onClick={() => setShowLogoutConfirm(false)}
                className={`p-2 rounded-xl transition-all duration-300 hover:scale-110 group ${
                  darkMode ? 'hover:bg-gray-700' : 'hover:bg-gray-100'
                }`}
              >
                <X size={20} className="group-hover:rotate-90 transition-transform duration-300" />
              </button>
            </div>

            {/* Contenido del Modal */}
            <div className={`p-6 rounded-xl border mb-6 ${
              darkMode ? 'bg-gray-700/50 border-gray-600' : 'bg-gray-50 border-gray-200'
            }`}>
              <div className="flex items-center gap-3 mb-3">
                <div className="p-2 bg-red-100 dark:bg-red-900/30 rounded-lg">
                  <X size={20} className="text-red-500" />
                </div>
                <span className={`font-medium ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                  ¿Estás seguro?
                </span>
              </div>
              <p className={`text-sm font-light tracking-wide ${darkMode ? 'text-gray-300' : 'text-gray-600'}`}>
                ¿Estás seguro de que deseas cerrar sesión? Serás redirigido al login principal del sistema.
              </p>
            </div>

            {/* Botones del Modal */}
            <div className="flex flex-col sm:flex-row gap-4">
              <button
                onClick={handleLogout}
                className="flex-1 flex items-center justify-center gap-3 bg-gradient-to-r from-red-600 to-red-700 hover:from-red-700 hover:to-red-800 text-white px-6 py-3 rounded-xl shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300 group"
              >
                <div className="p-1 bg-white/20 rounded-lg group-hover:bg-white/30 transition-all duration-300">
                  <X size={18} className="group-hover:rotate-90 transition-transform duration-300" />
                </div>
                <span className="font-light tracking-wide">Sí, Cerrar Sesión</span>
              </button>
              <button
                onClick={() => setShowLogoutConfirm(false)}
                className={`flex-1 flex items-center justify-center gap-3 px-6 py-3 rounded-xl shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300 group ${
                  darkMode
                    ? 'bg-gradient-to-r from-gray-700 to-gray-800 hover:from-gray-800 hover:to-gray-900 text-white'
                    : 'bg-gradient-to-r from-gray-200 to-gray-300 hover:from-gray-300 hover:to-gray-400 text-gray-900'
                }`}
              >
                <div className={`p-1 rounded-lg group-hover:bg-white/30 transition-all duration-300 ${
                  darkMode ? 'bg-white/20' : 'bg-black/10'
                }`}>
                  <X size={18} className="group-hover:rotate-90 transition-transform duration-300" />
                </div>
                <span className="font-light tracking-wide">Cancelar</span>
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default UsuarioDashboard;
