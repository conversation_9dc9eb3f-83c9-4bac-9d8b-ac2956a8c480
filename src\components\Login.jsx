import React, { useEffect, useRef, useState } from 'react';
import gsap from 'gsap';
import { useNavigate } from 'react-router-dom';
import secureStorage from '../utils/SecureStorage';
import securityMonitor from '../utils/SecurityMonitor';
import securityLogger from '../utils/SecurityLogger';

import logo1 from '../assets/sequre-logo-negro.svg';
import logo2 from '../assets/sequre-logo-negro-17.svg';
import logo3 from '../assets/sequre-logo-negro-18.svg';
import logo4 from '../assets/sequre-logo-negro-19.svg';
import logo5 from '../assets/sequre-logo-negro-20.svg';

const logoList = [logo1, logo2, logo3, logo4, logo5];

const Login = () => {
  const [usuario, setUsuario] = useState('');
  const [password, setPassword] = useState('');
  const [currentLogoIndex, setCurrentLogoIndex] = useState(0);
  const [loginAttempts, setLoginAttempts] = useState(0);
  const [isBlocked, setIsBlocked] = useState(false);
  const [errors, setErrors] = useState({});
  const [showSuccessNotification, setShowSuccessNotification] = useState(false);
  const [showErrorNotification, setShowErrorNotification] = useState(false);
  const [isRedirecting, setIsRedirecting] = useState(false);
  const [userType, setUserType] = useState('');
  const [errorMessage, setErrorMessage] = useState('');


  const navigate = useNavigate();

  const leftPanelRef = useRef(null);
  const rightLogoRef = useRef(null);
  const logoImgRef = useRef(null);

///-------------------------------------------------------------------------------------------------------------------------------------------------------------------///

// 🛡️ Funciones de Seguridad y Validación //

// Validación robusta de email
const validateEmail = (email) => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email) && email.length <= 254 && email.length >= 5;
};

// Validación de password
const validatePassword = (password) => {
  return password.length >= 6 && password.length <= 128;
};

// Sanitización de inputs
const sanitizeInput = (input) => {
  return input.trim().replace(/[<>'"]/g, '');
};

// Logging de seguridad (usando logger centralizado)
const logSecurityEvent = (event, details) => {
  securityLogger.logInfo(event, details);
};

// 🔐 Gestión de tokens (como estaba antes)
const TokenManager = {
  setToken: (token, role) => {
    localStorage.setItem('token', token);
    localStorage.setItem('userRole', role);

    logSecurityEvent('TOKEN_CREATED', {
      role: role,
      timestamp: new Date().toISOString()
    });
  },

  clearToken: () => {
    localStorage.removeItem('token');
    localStorage.removeItem('userRole');

    logSecurityEvent('TOKEN_CLEARED', {
      timestamp: new Date().toISOString()
    });
  },

  isTokenValid: () => {
    const token = localStorage.getItem('token');
    return !!token;
  },

  getRole: () => {
    return localStorage.getItem('userRole');
  }
};

///-------------------------------------------------------------------------------------------------------------------------------------------------------------------///

// Animacion logo //

// Este useEffect anima la entrada de los paneles izquierdo y derecho al cargar el componente.
// Además, agrega un listener que detecta si el usuario vuelve a la pestaña (visibilidad del documento),
// y fuerza que el logo tenga opacidad 1 por si quedó invisible.

useEffect(() => {
  let index = 0;

  const cycleLogo = () => {
    index = (index + 1) % logoList.length;

    // Anima el logo: desaparece → cambia → reaparece
    gsap.to(logoImgRef.current, {
      opacity: 0,
      duration: 0.2,
      onComplete: () => {
        setCurrentLogoIndex(index);
        gsap.to(logoImgRef.current, { opacity: 1, duration: 0.2 });
      },
    });
  };

  // ⏱ Intervalo cada 1 segundo
  const intervalId = setInterval(cycleLogo, 1000);

  // 🧼 Cleanup en desmontado
  return () => clearInterval(intervalId);
}, []);

// 🎬 Animación inicial de los paneles + corrección visual si cambia de pestaña
useEffect(() => {
  // Anima el panel izquierdo (formulario)
  gsap.fromTo(
    leftPanelRef.current,
    { y: 50, opacity: 0 },
    { y: 0, opacity: 1, duration: 1, ease: 'power2.out' }
  );

  // Anima el panel derecho (logo)
  gsap.fromTo(
    rightLogoRef.current,
    { y: 50, opacity: 0 },
    { y: 0, opacity: 1, duration: 1, ease: 'power2.out', delay: 0.5 }
  );

  // Asegura visibilidad del logo al volver de una pestaña oculta
  const handleVisibilityChange = () => {
    if (document.visibilityState === 'visible') {
      // Asegura que el logo esté visible al volver
      gsap.to(logoImgRef.current, { opacity: 1, duration: 0.2 });
    }
  };

  document.addEventListener('visibilitychange', handleVisibilityChange);

  return () => {
    document.removeEventListener('visibilitychange', handleVisibilityChange);
  };
}, []);

// 🛡️ Inicializar monitoreo de seguridad
useEffect(() => {
  // Iniciar monitoreo cuando se monta el componente
  securityMonitor.start();

  // Log de inicio de sesión de login
  logSecurityEvent('LOGIN_PAGE_ACCESSED', {
    timestamp: new Date().toISOString(),
    userAgent: navigator.userAgent,
    referrer: document.referrer,
    viewport: `${window.innerWidth}x${window.innerHeight}`,
    language: navigator.language,
    platform: navigator.platform
  });

  // Detectar si viene de logout o es primera visita
  const wasLoggedOut = sessionStorage.getItem('wasLoggedOut');
  if (wasLoggedOut) {
    logSecurityEvent('RETURN_AFTER_LOGOUT', {
      timestamp: new Date().toISOString()
    });
    sessionStorage.removeItem('wasLoggedOut');
  }

  // Limpiar al desmontar
  return () => {
    securityMonitor.stop();
  };
}, []);

///-------------------------------------------------------------------------------------------------------------------------------------------------------------------///

  // Hace un POST real al backend cuando esté listo.
  // Imprime el token que reciba (o puedes guardarlo en localStorage si lo necesitas).
  // Muestra errores claros si algo sale mal.

const handleSubmit = async (e) => {
  e.preventDefault();

  // 🚫 Verificar si está bloqueado por rate limiting
  if (isBlocked) {
    alert('⚠️ Demasiados intentos fallidos. Espera 5 minutos antes de intentar nuevamente.');
    logSecurityEvent('BLOCKED_LOGIN_ATTEMPT', {
      email: usuario.substring(0, 3) + '***',
      attempts: loginAttempts
    });
    return;
  }

  // 🧹 Limpiar errores previos
  setErrors({});

  // 🛡️ Sanitizar inputs
  const cleanEmail = sanitizeInput(usuario);
  const cleanPassword = sanitizeInput(password);

  // ✅ Validaciones robustas
  const newErrors = {};

  if (!cleanEmail || !cleanPassword) {
    newErrors.general = 'Todos los campos son obligatorios';
  }

  if (cleanEmail && !validateEmail(cleanEmail)) {
    newErrors.email = 'Formato de email inválido';
  }

  if (cleanPassword && !validatePassword(cleanPassword)) {
    newErrors.password = 'La contraseña debe tener entre 6 y 128 caracteres';
  }

  if (Object.keys(newErrors).length > 0) {
    setErrors(newErrors);
    logSecurityEvent('INVALID_INPUT_ATTEMPT', {
      email: cleanEmail.substring(0, 3) + '***',
      errors: Object.keys(newErrors)
    });
    return;
  }

  try {
    // 👨‍💼 Simulación de admin local (más segura)
    const isFakeAdmin = cleanEmail === '<EMAIL>' && cleanPassword === 'cityslicka';

    if (isFakeAdmin) {
      // ✅ Login exitoso de admin
      TokenManager.setToken('secure-admin-token-' + Date.now(), 'admin');

      logSecurityEvent('SUCCESSFUL_ADMIN_LOGIN', {
        email: '<EMAIL>',
        timestamp: new Date().toISOString()
      });

      setLoginAttempts(0); // Reset intentos

      // Mostrar notificación elegante y redirigir automáticamente
      setUserType('admin');
      setShowSuccessNotification(true);
      setIsRedirecting(true);

      setTimeout(() => {
        navigate('/admin');
      }, 2000); // Redirigir después de 2 segundos

      return;
    }

    // 🌐 Llamada real a reqres.in para usuarios normales (como estaba antes)
    console.log('🔍 Intentando login con reqres.in:', cleanEmail); // Debug

    const response = await fetch('https://reqres.in/api/login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'x-api-key': 'reqres-free-v1',
      },
      body: JSON.stringify({
        email: cleanEmail,
        password: cleanPassword,
      }),
    });

    const data = await response.json();
    console.log('📡 Respuesta de reqres.in:', response.status, data); // Debug

    if (response.ok) {
      // ✅ Login exitoso de usuario con token real de reqres.in
      TokenManager.setToken(data.token, 'usuario');

      logSecurityEvent('SUCCESSFUL_USER_LOGIN', {
        email: cleanEmail.substring(0, 3) + '***',
        timestamp: new Date().toISOString()
      });

      setLoginAttempts(0); // Reset intentos

      // Mostrar notificación elegante y redirigir automáticamente
      setUserType('usuario');
      setShowSuccessNotification(true);
      setIsRedirecting(true);

      setTimeout(() => {
        navigate('/usuario');
      }, 2000); // Redirigir después de 2 segundos
    } else {
      // ❌ Login fallido
      handleLoginFailure(cleanEmail, data.error || 'Credenciales inválidas');
    }
  } catch (error) {
    // ❌ Error inesperado
    console.error('Error inesperado:', error);
    handleLoginFailure(cleanEmail, 'Error inesperado del sistema');
  }
};

// 🚨 Manejo de fallos de login con rate limiting
const handleLoginFailure = (email, errorMessage) => {
  const newAttempts = loginAttempts + 1;
  setLoginAttempts(newAttempts);

  logSecurityEvent('FAILED_LOGIN_ATTEMPT', {
    email: email.substring(0, 3) + '***',
    attempt: newAttempts,
    error: errorMessage,
    ip: 'frontend-log' // En producción, el backend capturaría la IP real
  });

  if (newAttempts >= 5) {
    setIsBlocked(true);
    logSecurityEvent('USER_BLOCKED', {
      email: email.substring(0, 3) + '***',
      totalAttempts: newAttempts
    });

    // Desbloquear después de 5 minutos
    setTimeout(() => {
      setIsBlocked(false);
      setLoginAttempts(0);
      logSecurityEvent('USER_UNBLOCKED', {
        email: email.substring(0, 3) + '***'
      });
    }, 5 * 60 * 1000); // 5 minutos

    // Mostrar notificación de bloqueo
    setErrorMessage('🚫 Demasiados intentos fallidos. Cuenta bloqueada por 5 minutos.');
    setShowErrorNotification(true);

    // Ocultar notificación después de 4 segundos
    setTimeout(() => {
      setShowErrorNotification(false);
    }, 4000);
  } else {
    // Mostrar notificación de error con intentos restantes
    setErrorMessage(`❌ ${errorMessage}. Te quedan ${5 - newAttempts} intentos.`);
    setShowErrorNotification(true);

    // Ocultar notificación después de 3 segundos
    setTimeout(() => {
      setShowErrorNotification(false);
    }, 3000);
  }
};




///-------------------------------------------------------------------------------------------------------------------------------------------------------------------///
  // Renderiza el componente de inicio de sesión

  // con un formulario de usuario y contraseña, y un logo que cambia cada segundo.
  // El logo cambia con una animación suave y los paneles tienen una entrada animada.
  // El panel izquierdo contiene el formulario de inicio de sesión y el derecho muestra el logo.
  // El logo cambia cada segundo con una animación de desvanecimiento.
  // El formulario tiene campos para usuario y contraseña, y un botón para enviar.
  // El componente utiliza gsap para las animaciones y tiene un estado para manejar el usuario, contraseña y el índice del logo actual.
  return (
    <div className="flex flex-col lg:flex-row min-h-screen w-full overflow-hidden">
      {/* Panel izquierdo */}
      <div
        ref={leftPanelRef}
        className="w-full lg:w-1/2 flex flex-col justify-center items-center bg-white p-6 sm:p-8 lg:p-10"
      >
        <img src={logo1} alt="Logo" className="mb-4 sm:mb-6 w-32 sm:w-40" />
        <h2 className="text-lg sm:text-xl font-semibold mb-2 text-gray-800 text-center">¡Bienvenido a Quantum!</h2>
        <p className="text-gray-600 mb-4 sm:mb-6 text-center text-sm sm:text-base">Por favor inicia sesión para continuar. </p>

        {/* 🚨 Mostrar errores de validación */}
        {errors.general && (
          <div className="mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded-lg text-xs sm:text-sm w-full max-w-sm">
            {errors.general}
          </div>
        )}

        {/* ⚠️ Mostrar estado de bloqueo */}
        {isBlocked && (
          <div className="mb-4 p-3 bg-yellow-100 border border-yellow-400 text-yellow-700 rounded-lg text-xs sm:text-sm w-full max-w-sm">
            🚫 Cuenta temporalmente bloqueada por seguridad. Espera 5 minutos.
          </div>
        )}

        {/* 📊 Mostrar intentos restantes */}
        {loginAttempts > 0 && !isBlocked && (
          <div className="mb-4 p-3 bg-orange-100 border border-orange-400 text-orange-700 rounded-lg text-xs sm:text-sm w-full max-w-sm">
            ⚠️ Intentos fallidos: {loginAttempts}/5. Ten cuidado con las credenciales.
          </div>
        )}

        <form onSubmit={handleSubmit} className="w-full max-w-sm px-4 sm:px-0">
          <input
            type="email"
            placeholder="Email"
            value={usuario}
            onChange={(e) => setUsuario(e.target.value)}
            className={`w-full px-4 py-3 mb-2 border rounded-xl focus:outline-none focus:ring-2 transition text-sm sm:text-base ${
              errors.email
                ? 'border-red-400 focus:ring-red-400'
                : 'border-gray-300 focus:ring-indigo-400'
            }`}
            maxLength="254"
            required
            disabled={isBlocked}
          />
          {errors.email && (
            <p className="text-red-600 text-xs mb-2">{errors.email}</p>
          )}

          <input
            type="password"
            placeholder="Contraseña"
            value={password}
            onChange={(e) => setPassword(e.target.value)}
            className={`w-full px-4 py-3 mb-2 border rounded-xl focus:outline-none focus:ring-2 transition text-sm sm:text-base ${
              errors.password
                ? 'border-red-400 focus:ring-red-400'
                : 'border-gray-300 focus:ring-indigo-400'
            }`}
            maxLength="128"
            minLength="6"
            required
            disabled={isBlocked}
          />
          {errors.password && (
            <p className="text-red-600 text-xs mb-2">{errors.password}</p>
          )}
          <button
            type="submit"
            disabled={isBlocked}
            className={`w-full flex items-center justify-center gap-3 py-3 px-6 rounded-xl font-light tracking-wide shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300 group ${
              isBlocked
                ? 'bg-gray-400 text-gray-600 cursor-not-allowed'
                : 'bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white'
            }`}
          >
            {!isBlocked && (
              <div className="p-1 bg-white/20 rounded-lg group-hover:bg-white/30 transition-all duration-300">
                <svg className="w-4 h-4 group-hover:rotate-12 transition-transform duration-300" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M3 3a1 1 0 011 1v12a1 1 0 11-2 0V4a1 1 0 011-1zm7.707 3.293a1 1 0 010 1.414L9.414 9H17a1 1 0 110 2H9.414l1.293 1.293a1 1 0 01-1.414 1.414l-3-3a1 1 0 010-1.414l3-3a1 1 0 011.414 0z" clipRule="evenodd" />
                </svg>
              </div>
            )}
            <span className="font-light tracking-wide">
              {isBlocked ? '🚫 Bloqueado' : 'Iniciar Sesión'}
            </span>
          </button>
        </form>
      </div>

      {/* Panel derecho con cambio de logos */}
      <div
        ref={rightLogoRef}
        className="hidden lg:flex w-full lg:w-1/2 flex-col justify-center items-center bg-white p-6 sm:p-8 lg:p-10"
      >
        <img
          ref={logoImgRef}
          src={logoList[currentLogoIndex]}
          alt="Logo Quantum"
          className="w-2/3 max-w-md opacity-100 transition-opacity duration-500"
        />
      </div>

      {/* 🎉 Notificación de éxito elegante */}
      {showSuccessNotification && (
        <div className="fixed bottom-4 sm:bottom-8 left-1/2 transform -translate-x-1/2 z-50 px-4">
          <div className="bg-green-500 text-white px-4 sm:px-8 py-3 sm:py-4 rounded-lg shadow-2xl flex items-center gap-3 animate-bounce max-w-sm sm:max-w-none">
            <div className="w-5 h-5 sm:w-6 sm:h-6 bg-white rounded-full flex items-center justify-center flex-shrink-0">
              <span className="text-green-500 font-bold text-sm sm:text-lg">✓</span>
            </div>
            <div className="min-w-0">
              <p className="font-bold text-sm sm:text-lg">¡Login Exitoso!</p>
              <p className="text-xs sm:text-sm opacity-90 truncate">
                {isRedirecting
                  ? (userType === 'admin'
                      ? 'Redirigiendo al panel de administración...'
                      : 'Redirigiendo al panel de usuario...')
                  : (userType === 'admin'
                      ? 'Bienvenido Admin Quantum'
                      : 'Bienvenido Usuario Quantum')
                }
              </p>
            </div>
            {isRedirecting && (
              <div className="ml-2 sm:ml-4 flex-shrink-0">
                <div className="w-4 h-4 sm:w-5 sm:h-5 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
              </div>
            )}
          </div>
        </div>
      )}

      {/* 🚨 Notificación de error elegante */}
      {showErrorNotification && (
        <div className="fixed bottom-4 sm:bottom-8 left-1/2 transform -translate-x-1/2 z-50 px-4">
          <div className="bg-red-500 text-white px-4 sm:px-8 py-3 sm:py-4 rounded-lg shadow-2xl flex items-center gap-3 animate-bounce max-w-sm sm:max-w-md">
            <div className="w-5 h-5 sm:w-6 sm:h-6 bg-white rounded-full flex items-center justify-center flex-shrink-0">
              <span className="text-red-500 font-bold text-sm sm:text-lg">✗</span>
            </div>
            <div className="min-w-0">
              <p className="font-bold text-sm sm:text-lg">Error de Login</p>
              <p className="text-xs sm:text-sm opacity-90 break-words">
                {errorMessage}
              </p>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default Login;
