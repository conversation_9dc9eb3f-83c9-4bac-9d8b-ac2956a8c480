import { useState, useEffect, createContext, useContext } from 'react';

// Contexto de autenticación
const AuthContext = createContext();

// Provider de autenticación
export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Verificar si hay un usuario autenticado al cargar
    const token = sessionStorage.getItem('token') || localStorage.getItem('token');
    const userRole = sessionStorage.getItem('userRole') || localStorage.getItem('userRole');
    
    if (token && userRole) {
      // Simular datos de usuario basados en el rol
      if (userRole === 'admin') {
        setUser({
          id: 'admin-1',
          firstName: 'Admin',
          lastName: 'Quantum',
          email: '<EMAIL>',
          role: 'admin'
        });
      } else {
        setUser({
          id: 'user-1',
          firstName: 'Juan',
          lastName: 'Pérez',
          email: '<EMAIL>',
          role: 'usuario'
        });
      }
    }
    setIsLoading(false);
  }, []);

  const login = async (credentials) => {
    setIsLoading(true);
    try {
      // Simular login - en producción esto sería una llamada a la API
      if (credentials.email === '<EMAIL>') {
        const adminUser = {
          id: 'admin-1',
          firstName: 'Admin',
          lastName: 'Quantum',
          email: '<EMAIL>',
          role: 'admin'
        };
        setUser(adminUser);
        sessionStorage.setItem('token', 'admin-token');
        sessionStorage.setItem('userRole', 'admin');
        return { success: true, user: adminUser };
      } else {
        const regularUser = {
          id: 'user-1',
          firstName: 'Juan',
          lastName: 'Pérez',
          email: credentials.email,
          role: 'usuario'
        };
        setUser(regularUser);
        sessionStorage.setItem('token', 'user-token');
        sessionStorage.setItem('userRole', 'usuario');
        return { success: true, user: regularUser };
      }
    } catch (error) {
      return { success: false, error: error.message };
    } finally {
      setIsLoading(false);
    }
  };

  const logout = () => {
    setUser(null);
    sessionStorage.removeItem('token');
    sessionStorage.removeItem('userRole');
    sessionStorage.removeItem('tokenExpiry');
    sessionStorage.removeItem('loginTime');
    localStorage.removeItem('token');
    localStorage.removeItem('userRole');
    window.location.href = '/';
  };

  const value = {
    user,
    isLoading,
    login,
    logout
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};

// Hook para usar el contexto de autenticación
export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};
