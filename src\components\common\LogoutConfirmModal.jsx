import { useRef, useEffect } from 'react';
import { LogOut, X } from 'lucide-react';
import gsap from 'gsap';
import PropTypes from 'prop-types';
import { useLanguage } from '../../i18n';
import Button from './Button';

/**
 * Modal de confirmación para cerrar sesión
 */
const LogoutConfirmModal = ({ isOpen, onConfirm, onCancel, darkMode }) => {
  const { t } = useLanguage();
  const modalRef = useRef(null);
  const overlayRef = useRef(null);

  // Animaciones GSAP
  useEffect(() => {
    if (isOpen && modalRef.current && overlayRef.current) {
      // Bloquear scroll del body
      document.body.style.overflow = 'hidden';
      
      // Animación de entrada
      gsap.set(modalRef.current, { scale: 0.8, opacity: 0 });
      gsap.set(overlayRef.current, { opacity: 0 });
      
      gsap.to(overlayRef.current, { opacity: 1, duration: 0.3 });
      gsap.to(modalRef.current, { 
        scale: 1, 
        opacity: 1, 
        duration: 0.4, 
        ease: "back.out(1.7)" 
      });
    }

    return () => {
      // Restaurar scroll del body
      document.body.style.overflow = 'unset';
    };
  }, [isOpen]);

  const handleClose = () => {
    if (modalRef.current && overlayRef.current) {
      gsap.to(modalRef.current, { 
        scale: 0.8, 
        opacity: 0, 
        duration: 0.3 
      });
      gsap.to(overlayRef.current, { 
        opacity: 0, 
        duration: 0.3,
        onComplete: onCancel
      });
    } else {
      onCancel();
    }
  };

  const handleConfirm = () => {
    if (modalRef.current && overlayRef.current) {
      gsap.to(modalRef.current, { 
        scale: 0.8, 
        opacity: 0, 
        duration: 0.3 
      });
      gsap.to(overlayRef.current, { 
        opacity: 0, 
        duration: 0.3,
        onComplete: onConfirm
      });
    } else {
      onConfirm();
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center p-4">
      {/* Overlay */}
      <div 
        ref={overlayRef}
        className="absolute inset-0 bg-black bg-opacity-50"
        onClick={handleClose}
      />
      
      {/* Modal */}
      <div
        ref={modalRef}
        className={`relative w-full max-w-md p-8 rounded-3xl shadow-2xl border ${
          darkMode
            ? 'bg-gray-800 border-gray-700 text-white'
            : 'bg-white border-gray-100 text-gray-900'
        }`}
      >
        {/* Botón cerrar */}
        <button
          onClick={handleClose}
          className={`absolute top-6 right-6 p-2 rounded-full transition-all duration-300 ${
            darkMode
              ? 'hover:bg-gray-700 text-gray-400 hover:text-white'
              : 'hover:bg-gray-100 text-gray-500 hover:text-gray-700'
          }`}
        >
          <X size={18} />
        </button>

        {/* Icono principal */}
        <div className="flex justify-center mb-8">
          <div className="w-20 h-20 rounded-full bg-gradient-to-br from-red-500 to-rose-600 flex items-center justify-center shadow-xl">
            <LogOut size={32} className="text-white" />
          </div>
        </div>

        {/* Título */}
        <h2 className="text-2xl font-light tracking-wide text-center mb-6">
          {t('logout.confirmTitle')}
        </h2>

        {/* Mensaje */}
        <p className={`text-center mb-8 font-light tracking-wide leading-relaxed ${
          darkMode ? 'text-gray-300' : 'text-gray-600'
        }`}>
          {t('logout.confirmMessage')}
        </p>

        {/* Botones */}
        <div className="flex gap-4">
          <button
            onClick={handleClose}
            className={`flex-1 px-6 py-3 rounded-xl font-light tracking-wide transition-all duration-300 border-2 ${
              darkMode
                ? 'border-gray-600 text-gray-300 hover:bg-gray-700 hover:border-gray-500'
                : 'border-gray-300 text-gray-700 hover:bg-gray-50 hover:border-gray-400'
            }`}
          >
            {t('logout.cancel')}
          </button>

          <button
            onClick={handleConfirm}
            className="flex-1 px-6 py-3 rounded-xl font-light tracking-wide text-white bg-gradient-to-r from-red-500 to-rose-600 hover:from-red-600 hover:to-rose-700 shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300"
          >
            {t('logout.confirm')}
          </button>
        </div>
      </div>
    </div>
  );
};

LogoutConfirmModal.propTypes = {
  isOpen: PropTypes.bool.isRequired,
  onConfirm: PropTypes.func.isRequired,
  onCancel: PropTypes.func.isRequired,
  darkMode: PropTypes.bool.isRequired
};

export default LogoutConfirmModal;
