import { useState } from 'react';
import { Plus, Eye, EyeOff } from 'lucide-react';
import PropTypes from 'prop-types';
import { Modal, Button } from '../../common';
// import { validationRules } from '../../../utils/validations';

/**
 * Modal para crear nuevo usuario
 */
const UserCreateModal = ({ isOpen, onClose, onCreate, darkMode }) => {
  const [formData, setFormData] = useState({
    email: '',
    firstName: '',
    lastName: '',
    company: '',
    password: '',
    role: 'USER',
    ctmIpAddress: '',
    ctmUsername: '',
    ctmPassword: '',
    ctmDomain: '',
    seqrngIpAddress: '',
    seqrngApiToken: ''
  });
  const [isSaving, setIsSaving] = useState(false);
  const [errors, setErrors] = useState({});
  const [showPassword, setShowPassword] = useState(false);

  const handleChange = (field, value) => {
    setFormData(prev => ({ ...prev, [field]: value }));

    // Limpiar error del campo cuando el usuario empiece a escribir
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: null }));
    }
  };

  const validateForm = () => {
    const newErrors = {};

    // Validar campos requeridos
    if (!formData.firstName) newErrors.firstName = 'El nombre es requerido';
    if (!formData.lastName) newErrors.lastName = 'El apellido es requerido';
    if (!formData.email) newErrors.email = 'El email es requerido';
    if (!formData.password) newErrors.password = 'La contraseña es requerida';

    // Validar contraseña con las nuevas reglas
    if (formData.password) {
      const passwordError = validationRules.password(formData.password);
      if (passwordError) {
        newErrors.password = passwordError;
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setIsSaving(true);
    try {
      await onCreate(formData);
      // Reset form
      setFormData({
        email: '',
        firstName: '',
        lastName: '',
        company: '',
        password: '',
        role: 'USER',
        ctmIpAddress: '',
        ctmUsername: '',
        ctmPassword: '',
        ctmDomain: '',
        seqrngIpAddress: '',
        seqrngApiToken: ''
      });
      setErrors({});
      setShowPassword(false);
    } finally {
      setIsSaving(false);
    }
  };

  const roleOptions = [
    { value: 'USER', label: 'Usuario' },
    { value: 'ADMIN', label: 'Administrador' }
  ];

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title="Crear Nuevo Usuario"
      maxWidth="max-w-4xl"
      darkMode={darkMode}
      className="max-h-[90vh] overflow-y-auto"
    >
      <form onSubmit={handleSubmit}>
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Información Personal */}
          <div className="space-y-4 p-6 rounded-xl border-2 border-gray-200 dark:border-gray-600 bg-gray-50/50 dark:bg-gray-700/30 shadow-sm">
            <h4 className="text-lg font-light tracking-wide leading-relaxed border-b-2 border-blue-200 dark:border-blue-700 pb-3 mb-4 text-gray-800 dark:text-gray-200 flex items-center gap-2">
              <span className="text-blue-500">👤</span> Información Personal
            </h4>

            <FormField
              label="Nombre"
              type="text"
              value={formData.firstName}
              onChange={(e) => handleChange('firstName', e.target.value)}
              required
              darkMode={darkMode}
            />

            <FormField
              label="Apellido"
              type="text"
              value={formData.lastName}
              onChange={(e) => handleChange('lastName', e.target.value)}
              required
              darkMode={darkMode}
            />

            <FormField
              label="Email"
              type="email"
              value={formData.email}
              onChange={(e) => handleChange('email', e.target.value)}
              required
              darkMode={darkMode}
            />

            <div className="relative">
              <FormField
                label="Contraseña"
                type={showPassword ? "text" : "password"}
                value={formData.password}
                onChange={(e) => handleChange('password', e.target.value)}
                required
                disabled={isSaving}
                darkMode={darkMode}
                className="pr-10"
              />
              <button
                type="button"
                onClick={() => setShowPassword(!showPassword)}
                className="absolute right-3 top-8 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
                disabled={isSaving}
              >
                {showPassword ? <EyeOff size={20} /> : <Eye size={20} />}
              </button>
              {errors.password && (
                <p className="text-red-500 text-sm mt-1">{errors.password}</p>
              )}
            </div>

            {/* Información sobre requisitos de contraseña */}
            <div className={`p-3 rounded-lg border ${
              darkMode ? 'bg-blue-900/20 border-blue-700' : 'bg-blue-50 border-blue-200'
            }`}>
              <h6 className="font-medium text-blue-800 dark:text-blue-300 mb-1 text-sm">
                📋 Requisitos de Contraseña
              </h6>
              <ul className="text-xs text-blue-700 dark:text-blue-300 space-y-1">
                <li>• Mínimo 8 caracteres</li>
                <li>• Caracteres alfanuméricos y símbolos permitidos</li>
              </ul>
            </div>

            <FormField
              label="Empresa"
              type="text"
              value={formData.company}
              onChange={(e) => handleChange('company', e.target.value)}
              darkMode={darkMode}
            />

            <FormField
              label="Rol"
              type="select"
              value={formData.role}
              onChange={(e) => handleChange('role', e.target.value)}
              options={roleOptions}
              darkMode={darkMode}
            />
          </div>

          {/* Configuración de Servicios */}
          <div className="space-y-4 p-6 rounded-xl border-2 border-gray-200 dark:border-gray-600 bg-gray-50/50 dark:bg-gray-700/30 shadow-sm">
            <h4 className="text-lg font-light tracking-wide leading-relaxed border-b-2 border-purple-200 dark:border-purple-700 pb-3 mb-4 text-gray-800 dark:text-gray-200 flex items-center gap-2">
              <span className="text-purple-500">⚙️</span> Configuración de Servicios
            </h4>

            {/* CTM Configuration */}
            <div className="space-y-3 p-4 bg-blue-50/70 dark:bg-blue-900/20 rounded-lg border border-blue-200/60 dark:border-blue-800/60">
              <h5 className="font-light tracking-wide text-blue-700 dark:text-blue-300 flex items-center gap-2 border-b border-blue-200 dark:border-blue-700 pb-2 mb-3">
                <span className="text-blue-600">🔐</span> CipherTrust Manager
              </h5>

              <FormField
                label="IP Address"
                type="text"
                value={formData.ctmIpAddress}
                onChange={(e) => handleChange('ctmIpAddress', e.target.value)}
                placeholder="https://ctm.example.com:443"
                darkMode={darkMode}
              />

              <FormField
                label="Username"
                type="text"
                value={formData.ctmUsername}
                onChange={(e) => handleChange('ctmUsername', e.target.value)}
                darkMode={darkMode}
              />

              <FormField
                label="Password"
                type="password"
                value={formData.ctmPassword}
                onChange={(e) => handleChange('ctmPassword', e.target.value)}
                darkMode={darkMode}
              />

              <FormField
                label="Domain"
                type="text"
                value={formData.ctmDomain}
                onChange={(e) => handleChange('ctmDomain', e.target.value)}
                placeholder="root"
                darkMode={darkMode}
              />
            </div>

            {/* SEQRNG Configuration */}
            <div className="space-y-3 p-4 bg-green-50/70 dark:bg-green-900/20 rounded-lg border border-green-200/60 dark:border-green-800/60">
              <h5 className="font-light tracking-wide text-green-700 dark:text-green-300 flex items-center gap-2 border-b border-green-200 dark:border-green-700 pb-2 mb-3">
                <span className="text-green-600">🎲</span> SEQRNG
              </h5>

              <FormField
                label="IP Address"
                type="text"
                value={formData.seqrngIpAddress}
                onChange={(e) => handleChange('seqrngIpAddress', e.target.value)}
                placeholder="https://seqrng.example.com:1982"
                darkMode={darkMode}
              />

              <FormField
                label="API Token"
                type="text"
                value={formData.seqrngApiToken}
                onChange={(e) => handleChange('seqrngApiToken', e.target.value)}
                placeholder="1|cifovFzUWTeAPUeAYlZYefsY3tXFf9Z1TV5WV0YL"
                darkMode={darkMode}
              />
            </div>
          </div>
        </div>

        {/* Footer fijo centrado */}
        <div className="flex justify-center gap-4 mt-6 pt-6 border-t-2 border-gray-200 dark:border-gray-600 bg-gray-50/30 dark:bg-gray-700/20 px-8 pb-6 flex-shrink-0 rounded-b-2xl -mx-8">
          <button
            type="submit"
            disabled={isSaving}
            className="px-4 py-2 rounded-xl font-light tracking-wide shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300 group bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 text-white disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2"
          >
            <div className="p-1 bg-white/20 rounded-lg group-hover:bg-white/30 transition-all duration-300">
              <Plus size={14} className={isSaving ? "animate-spin" : ""} />
            </div>
            <span>
              {isSaving ? 'Creando Usuario...' : 'Crear Usuario'}
            </span>
          </button>

          <button
            type="button"
            onClick={onClose}
            disabled={isSaving}
            className="px-4 py-2 rounded-xl font-light tracking-wide border-2 border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 hover:border-gray-400 dark:hover:border-gray-500 transform hover:scale-105 transition-all duration-300 disabled:opacity-50"
          >
            Cancelar
          </button>
        </div>
      </form>
    </Modal>
  );
};

UserCreateModal.propTypes = {
  isOpen: PropTypes.bool.isRequired,
  onClose: PropTypes.func.isRequired,
  onCreate: PropTypes.func.isRequired,
  darkMode: PropTypes.bool.isRequired
};

export default UserCreateModal;
