import { useEffect, useRef, useState } from "react";
import gsap from "gsap";
import {
  Sun,
  Moon,
  Users,
  KeyRound,
  BarChart3,
  Activity,
  Edit,
  Trash2,
  X,
  Save,
  User,
  Mail,
} from "lucide-react";
import secureStorage from '../utils/SecureStorage';
import securityMonitor from '../utils/SecurityMonitor';
import { useAuth, useUsers } from '../hooks/index';
import { useAdminStats } from '../hooks/useAdminStats';
import { DashboardLayout, DashboardStats } from '../components/dashboard';
import { UserManagement } from '../components/dashboard/admin';
import { useLanguage } from '../i18n';

// Información del admin (estática)
const adminInfo = {
  name: "Admin Quantum",
  email: "<EMAIL>"
};

const AdminDashboard = () => {
  const sidebarRef = useRef(null);
  const mainContentRef = useRef(null);
  const [darkMode, setDarkMode] = useState(false);
  const [activeSection, setActiveSection] = useState('dashboard');
  const [showEditModal, setShowEditModal] = useState(false);
  const [editingUser, setEditingUser] = useState(null);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [userToDelete, setUserToDelete] = useState(null);
  const [newUser, setNewUser] = useState({
    name: '',
    apellido: '',
    email: '',
    password: '',
    empresa: '',
    rol: 'Usuario'
  });
  const [showLogoutConfirm, setShowLogoutConfirm] = useState(false);

  // Hooks para autenticación y usuarios
  const { user: currentUser, logout } = useAuth();
  const { t } = useLanguage();
  const {
    users,
    isLoading: usersLoading,
    error: usersError,
    getAllUsers,
    createUser,
    updateUser,
    deleteUser,
    clearError
  } = useUsers();
  const {
    stats,
    isLoading: statsLoading,
    error: statsError,
    loadStats,
    clearError: clearStatsError
  } = useAdminStats();

  // 🔁 Animaciones GSAP al montar (solo escritorio)
  useEffect(() => {
    if (window.innerWidth >= 768) {
      gsap.fromTo(
        sidebarRef.current,
        { x: -100, opacity: 0 },
        { x: 0, opacity: 1, duration: 1, ease: "power2.out" }
      );
    }

    gsap.fromTo(
      mainContentRef.current,
      { y: 100, opacity: 0 },
      { y: 0, opacity: 1, duration: 1, ease: "power2.out", delay: 0.3 }
    );
  }, []);

  // 🛡️ Inicializar monitoreo de seguridad para admin
  useEffect(() => {
    securityMonitor.start();

    // Log específico de acceso admin
    console.log('[ADMIN_SECURITY] Admin dashboard accessed at:', new Date().toISOString());
    console.log('[ADMIN_SECURITY] Admin info:', adminInfo);

    // Verificar integridad de datos de admin
    const adminRole = secureStorage.getSecure('user_role') || sessionStorage.getItem('userRole');
    if (adminRole !== 'admin') {
      console.error('[ADMIN_SECURITY] Invalid admin role detected:', adminRole);
      handleLogout();
    }

    return () => {
      securityMonitor.stop();
    };
  }, []); // adminInfo es estático, no necesita dependencia

  // 🔁 Toggle de modo oscuro
  const toggleDarkMode = () => {
    const newDarkMode = !darkMode;
    setDarkMode(newDarkMode);

    // Agregar/quitar clase 'dark' del HTML para que Tailwind funcione
    if (newDarkMode) {
      document.documentElement.classList.add('dark');
    } else {
      document.documentElement.classList.remove('dark');
    }
  };

  // 🔁 Cerrar sesión seguro
  const handleLogout = () => {
    // Log de logout de admin
    console.log('[ADMIN_SECURITY] Admin logout initiated at:', new Date().toISOString());

    // Detener monitoreo
    securityMonitor.stop();

    // Limpiar datos encriptados
    secureStorage.clearAllSecure();

    // Limpiar todos los tokens y datos de sesión
    sessionStorage.removeItem("token");
    sessionStorage.removeItem("userRole");
    sessionStorage.removeItem("tokenExpiry");
    sessionStorage.removeItem("loginTime");
    localStorage.removeItem("token"); // Legacy cleanup
    localStorage.removeItem("userRole"); // Legacy cleanup

    // Marcar que fue un logout intencional
    sessionStorage.setItem('wasLoggedOut', 'true');

    // Log final de seguridad
    console.log('[ADMIN_SECURITY] Admin logout completed at:', new Date().toISOString());

    window.location.href = "/";
  };

  // 🔁 Funciones para gestión de usuarios
  const handleEditUser = (user) => {
    setEditingUser({ ...user });
    setShowEditModal(true);
  };

  const handleDeleteUser = (user) => {
    setUserToDelete(user);
    setShowDeleteModal(true);
  };

  const confirmDeleteUser = () => {
    setUsers(users.filter(user => user.id !== userToDelete.id));
    setShowDeleteModal(false);
    setUserToDelete(null);
  };

  const saveUserChanges = () => {
    setUsers(users.map(user =>
      user.id === editingUser.id ? editingUser : user
    ));
    setShowEditModal(false);
    setEditingUser(null);
  };

  const handleCreateUser = () => {
    setShowCreateModal(true);
  };

  const handleNewUserInputChange = (field, value) => {
    setNewUser(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const createNewUser = () => {
    const newUserData = {
      id: users.length + 1,
      name: `${newUser.name} ${newUser.apellido}`,
      email: newUser.email,
      status: 'activo',
      keys: []
    };

    setUsers([...users, newUserData]);
    setShowCreateModal(false);
    setNewUser({
      name: '',
      apellido: '',
      email: '',
      password: '',
      empresa: '',
      rol: 'Usuario'
    });
  };

  // Funciones para gestión de usuarios
  const handleUpdateUser = async (userId, userData) => {
    await updateUser(userId, userData);
    // Refrescar datos después de actualizar
    await Promise.all([getAllUsers(), loadStats()]);
  };

  const handleCreateUser = async (userData) => {
    await createUser(userData);
    // Refrescar datos después de crear
    await Promise.all([getAllUsers(), loadStats()]);
  };

  const handleDeleteUser = async (userId) => {
    try {
      // Usar el hook deleteUser que ya maneja la actualización de la lista
      await deleteUser(userId);

      // Refrescar estadísticas después de eliminar
      await loadStats();

      // TODO: Mostrar notificación elegante de éxito (reemplazar alert)
      alert('Usuario eliminado exitosamente');

    } catch (error) {
      console.error('Error eliminando usuario:', error);
      // TODO: Mostrar notificación elegante de error (reemplazar alert)
      alert('Error al eliminar el usuario: ' + error.message);
    }
  };

  const handleGetUserKeys = async (userId) => {
    try {
      // TODO: Implementar endpoint específico para obtener llaves de un usuario
      // Por ahora, buscar en usersWithKeys si el usuario tiene llaves
      const userWithKeys = usersWithKeys.find(u => u.id === userId);
      return userWithKeys?.keys || [];
    } catch (error) {
      console.error('Error getting user keys:', error);
      return [];
    }
  };

  // Configuración de navegación
  const navigationItems = [
    { key: 'dashboard', label: t('navigation.dashboard'), icon: BarChart3 },
    { key: 'users', label: t('navigation.users'), icon: Users },
    { key: 'keys', label: 'Llaves', icon: KeyRound }
  ];

  // Configuración de estadísticas principales
  const mainStats = [
    {
      icon: Users,
      title: t('dashboard.totalUsers'),
      value: stats.totalUsers,
      description: "Usuarios registrados en el sistema.",
      iconColor: "text-blue-500",
      valueColor: "text-blue-600 dark:text-blue-400"
    },
    {
      icon: KeyRound,
      title: t('dashboard.activeKeys'),
      value: stats.activeKeys,
      description: "Llaves subidas a CTM exitosamente.",
      iconColor: "text-green-500",
      valueColor: "text-green-600 dark:text-green-400"
    },
    {
      icon: Activity,
      title: t('dashboard.activeUsers'),
      value: stats.activeUsers,
      description: "Usuarios con estado activo.",
      iconColor: "text-purple-500",
      valueColor: "text-purple-600 dark:text-purple-400"
    }
  ];

  // Configuración de estadísticas adicionales
  const miniStats = [
    {
      value: stats.totalKeys,
      label: t('dashboard.totalKeys'),
      valueColor: "text-gray-900 dark:text-white"
    },
    {
      value: stats.successfulKeys,
      label: t('dashboard.successful'),
      valueColor: "text-green-600 dark:text-green-400"
    },
    {
      value: stats.failedKeys,
      label: t('dashboard.failed'),
      valueColor: "text-red-600 dark:text-red-400"
    },
    {
      value: stats.uploadedToCtm,
      label: t('dashboard.inCTM'),
      valueColor: "text-blue-600 dark:text-blue-400"
    }
  ];

  const renderContent = (darkMode = false) => {
    switch (activeSection) {
      case 'dashboard':
        return (
          <>
            <h1 className="text-3xl font-light tracking-wide leading-relaxed mb-4">
              {t('dashboard.adminWelcome')}
            </h1>
            <p className="text-gray-600 dark:text-white mb-6 font-light tracking-wide">
              {t('dashboard.adminSubtitle')}
            </p>

            <DashboardStats
              mainStats={mainStats}
              miniStats={miniStats}
              isLoading={statsLoading}
              error={statsError}
              onClearError={clearStatsError}
            />
          </>
        );

      case 'users':
        return (
          <UserManagement
            users={users}
            usersWithKeys={usersWithKeys}
            isLoading={usersLoading || statsLoading}
            error={usersError}
            onClearError={clearError}
            onUpdateUser={handleUpdateUser}
            onCreateUser={handleCreateUser}
            onDeleteUser={handleDeleteUser}
            onGetUserKeys={handleGetUserKeys}
            darkMode={darkMode}
          />
        );

      case 'keys':
        return (
          <div>
            <h1 className="text-3xl font-bold mb-4">Gestión de Llaves</h1>
            <p className="text-gray-600 dark:text-white">
              Sección en desarrollo para gestionar todas las llaves API del sistema.
            </p>
          </div>
        );



      default:
        return null;
    }
  };

  // Componente wrapper que recibe darkMode del DashboardLayout
  const ContentWrapper = ({ darkMode }) => {
    return renderContent(darkMode);

  };

  return (
    <div
      className={`flex flex-col lg:flex-row min-h-screen transition-colors duration-500 ${
        darkMode ? "bg-gray-900 text-white" : "bg-gray-200 text-gray-900"
      }`}
    >
      {/* 🌙 Sidebar siempre visible (responsive como Gmail) */}
      <aside
        ref={sidebarRef}
        className={`w-full lg:w-64 xl:w-72 lg:h-screen shadow-xl z-10 p-4 lg:p-6 flex flex-col justify-between transition-colors duration-500
        ${darkMode ? "bg-gray-800 text-white" : "bg-white text-gray-800"}`}
      >
        {/* 🔹 Información del Admin */}
        <div>
          <div className="flex justify-center items-center gap-3 mb-4">
            <img
              src="/src/assets/logo-usuario.png"
              alt="Quantum Logo"
              className="w-10 h-10"
            />
            <img
              src={darkMode ? "/src/assets/secure-logo-blanco.png" : "/src/assets/sequre-logo-negro.svg"}
              alt="Segundo Logo"
              className="w-100 h-10"
            />
          </div>
          
          {/* Información del administrador */}
          <div className={`mb-8 p-4 rounded-lg border shadow-lg hover:shadow-xl transition-all duration-300 ${darkMode ? 'bg-gray-700 border-gray-600' : 'bg-gray-50 border-gray-200'}`}>
            <div className="flex items-center gap-3 mb-2">
              <User size={16} className="text-blue-500" />
              <span className="font-medium text-sm">{adminInfo.name}</span>
            </div>
            <div className="flex items-center gap-3">
              <Mail size={16} className="text-green-500" />
              <span className="text-sm text-gray-600 dark:text-white">{adminInfo.email}</span>
            </div>
          </div>

          <nav className="space-y-4">
            <button
              onClick={() => setActiveSection('dashboard')}
              className={`flex items-center gap-3 px-4 py-3 rounded-xl transition-all duration-300 w-full text-left hover:shadow-lg ${
                activeSection === 'dashboard'
                  ? (darkMode ? 'bg-blue-900 text-blue-300 shadow-lg' : 'bg-blue-100 text-blue-700 shadow-lg')
                  : (darkMode ? 'hover:bg-gray-700' : 'hover:bg-gray-100')
              }`}
            >
              <BarChart3 size={18} />
              <span className={`font-light tracking-wide ${darkMode ? 'text-white' : 'text-gray-800'}`}>
                Dashboard
              </span>
            </button>
            <button
              onClick={() => setActiveSection('users')}
              className={`flex items-center gap-3 px-4 py-3 rounded-xl transition-all duration-300 w-full text-left hover:shadow-lg ${
                activeSection === 'users'
                  ? (darkMode ? 'bg-blue-900 text-blue-300 shadow-lg' : 'bg-blue-100 text-blue-700 shadow-lg')
                  : (darkMode ? 'hover:bg-gray-700' : 'hover:bg-gray-100')
              }`}
            >
              <Users size={18} />
              <span className={`font-light tracking-wide ${darkMode ? 'text-white' : 'text-gray-800'}`}>
                Usuarios
              </span>
            </button>
            <button
              onClick={() => setActiveSection('keys')}
              className={`flex items-center gap-3 px-4 py-3 rounded-xl transition-all duration-300 w-full text-left hover:shadow-lg ${
                activeSection === 'keys'
                  ? (darkMode ? 'bg-blue-900 text-blue-300 shadow-lg' : 'bg-blue-100 text-blue-700 shadow-lg')
                  : (darkMode ? 'hover:bg-gray-700' : 'hover:bg-gray-100')
              }`}
            >
              <KeyRound size={18} />
              <span className={`font-light tracking-wide ${darkMode ? 'text-white' : 'text-gray-800'}`}>
                Llaves
              </span>
            </button>

          </nav>
        </div>

        {/* 🔘 Botones: modo claro/oscuro + logout */}
        <div className="flex flex-col gap-4">
          <button
            onClick={toggleDarkMode}
            className={`flex items-center justify-center gap-3 w-full px-4 py-3 rounded-xl shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300 group
              ${darkMode
                ? "bg-gradient-to-r from-yellow-600 to-yellow-700 hover:from-yellow-700 hover:to-yellow-800 text-white border border-yellow-500"
                : "bg-gradient-to-r from-gray-600 to-gray-700 hover:from-gray-700 hover:to-gray-800 text-white border border-gray-500"}`}
          >
            {darkMode ? (
              <>
                <div className="p-1 bg-white/20 rounded-lg group-hover:bg-white/30 transition-all duration-300">
                  <Sun size={18} className="text-yellow-300 animate-pulse group-hover:rotate-180 transition-transform duration-500" />
                </div>
                <span className="font-light tracking-wide">Modo Claro</span>
              </>
            ) : (
              <>
                <div className="p-1 bg-white/20 rounded-lg group-hover:bg-white/30 transition-all duration-300">
                  <Moon size={18} className="text-indigo-300 animate-pulse group-hover:rotate-12 transition-transform duration-300" />
                </div>
                <span className="font-light tracking-wide">Modo Oscuro</span>
              </>
            )}
          </button>

          <button
            onClick={() => setShowLogoutConfirm(true)}
            className="flex items-center justify-center gap-3 w-full px-4 py-3 rounded-xl bg-gradient-to-r from-red-600 to-red-700 hover:from-red-700 hover:to-red-800 text-white shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300 group"
          >
            <div className="p-1 bg-white/20 rounded-lg group-hover:bg-white/30 transition-all duration-300">
              <X size={18} className="group-hover:rotate-90 transition-transform duration-300" />
            </div>
            <span className="font-light tracking-wide">Cerrar Sesión</span>
          </button>
        </div>
      </aside>

      {/* 📄 Contenido Principal */}
      <main
        ref={mainContentRef}
        className={`flex-1 p-4 sm:p-6 lg:p-10 relative transition-colors duration-500 flex flex-col
          ${darkMode ? "bg-gray-700" : "bg-gray-50"}`}
      >


        {/* 🧩 Contenedor principal */}
        <div
          className={`relative z-10 transition-all duration-500 rounded-xl shadow-md border max-h-[calc(100vh-16rem)] overflow-hidden
            ${darkMode
              ? "bg-gray-800 border-gray-700 text-white"
              : "bg-white border-gray-200 text-gray-900"}`}
        >
          <div className="h-full overflow-y-auto scrollbar-thin p-6 sm:p-8">
          {/* Renderizado condicional según la sección activa */}
          {activeSection === 'dashboard' && (
            <>
              <h1 className={`text-4xl font-black tracking-wider uppercase mb-4 ${darkMode ? 'text-white' : 'text-gray-700'}`}>
                BIENVENIDO, {adminInfo.name.toUpperCase()}
              </h1>
              <p className={`text-lg font-light tracking-wide leading-relaxed mb-6 ${darkMode ? 'text-gray-200' : 'text-gray-700'}`}>
                <span className={`font-medium ${darkMode ? 'text-white' : 'text-gray-800'}`}>Sistema QRNG Quantum</span> •
                Gestiona usuarios, llaves cuánticas y configuraciones del sistema con tecnología de vanguardia.
              </p>

              {/* 📊 Tarjetas de información */}
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
                <div className={`p-6 rounded-xl border shadow-lg hover:shadow-2xl hover:-translate-y-1 hover:scale-[1.02] transition-all duration-300 group cursor-pointer ${darkMode ? 'bg-gray-700 border-gray-600' : 'bg-white border-gray-200'}`}>
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center gap-3">
                      <div className="p-2 bg-blue-100 dark:bg-blue-900/30 rounded-lg group-hover:scale-125 group-hover:rotate-12 transition-all duration-500">
                        <Users size={20} className="text-blue-500 group-hover:text-blue-600" />
                      </div>
                      <span className={`font-semibold group-hover:text-blue-600 transition-colors duration-300 ${darkMode ? 'text-white' : 'text-gray-900'}`}>Total Usuarios</span>
                    </div>
                  </div>

                  <div className="flex items-end justify-between">
                    <div>
                      <p className="text-3xl font-bold text-blue-600 dark:text-blue-400 mb-1 group-hover:scale-110 transition-transform duration-300">
                        {users.length}
                      </p>
                      <p className={`text-sm ${darkMode ? 'text-gray-300' : 'text-gray-600'}`}>
                        Usuarios registrados en el sistema
                      </p>
                    </div>
                    <div className="relative w-16 h-16 group-hover:scale-110 transition-transform duration-300">
                      <svg className="w-16 h-16 transform -rotate-90" viewBox="0 0 36 36">
                        <path
                          d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
                          fill="none"
                          stroke={darkMode ? "#374151" : "#e5e7eb"}
                          strokeWidth="3"
                        />
                        <path
                          d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
                          fill="none"
                          stroke="#3b82f6"
                          strokeWidth="3"
                          strokeDasharray={`${(users.length / 10) * 100}, 100`}
                          className="transition-all duration-1000 delay-300 group-hover:stroke-blue-600"
                        />
                      </svg>
                      <div className="absolute inset-0 flex items-center justify-center">
                        <span className="text-xs font-bold text-blue-600 dark:text-white">
                          {Math.round((users.length / 10) * 100)}%
                        </span>
                      </div>
                    </div>
                  </div>
                </div>

                <div className={`p-6 rounded-xl border shadow-lg hover:shadow-2xl hover:-translate-y-1 hover:scale-[1.02] transition-all duration-300 group cursor-pointer ${darkMode ? 'bg-gray-700 border-gray-600' : 'bg-white border-gray-200'}`}>
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center gap-3">
                      <div className="p-2 bg-green-100 dark:bg-green-900/30 rounded-lg group-hover:scale-125 group-hover:rotate-12 transition-all duration-500">
                        <KeyRound size={20} className="text-green-500 group-hover:text-green-600" />
                      </div>
                      <span className={`font-semibold group-hover:text-green-600 transition-colors duration-300 ${darkMode ? 'text-white' : 'text-gray-900'}`}>Llaves Activas</span>
                    </div>
                  </div>

                  <div className="flex items-end justify-between">
                    <div>
                      <p className="text-3xl font-bold text-green-600 dark:text-green-400 mb-1 group-hover:scale-110 transition-transform duration-300">
                        {users.reduce((total, user) => total + user.keys.filter(key => key.status === 'activa').length, 0)}
                      </p>
                      <p className={`text-sm ${darkMode ? 'text-gray-300' : 'text-gray-600'}`}>
                        Llaves API en funcionamiento
                      </p>
                    </div>
                    <div className="relative w-16 h-16 group-hover:scale-110 transition-transform duration-300">
                      <svg className="w-16 h-16 transform -rotate-90" viewBox="0 0 36 36">
                        <path
                          d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
                          fill="none"
                          stroke={darkMode ? "#374151" : "#e5e7eb"}
                          strokeWidth="3"
                        />
                        <path
                          d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
                          fill="none"
                          stroke="#10b981"
                          strokeWidth="3"
                          strokeDasharray={`${Math.round((users.reduce((total, user) => total + user.keys.filter(key => key.status === 'activa').length, 0) / users.reduce((total, user) => total + user.keys.length, 0)) * 100) || 0}, 100`}
                          className="transition-all duration-1000 delay-300 group-hover:stroke-green-600"
                        />
                      </svg>
                      <div className="absolute inset-0 flex items-center justify-center">
                        <span className="text-xs font-bold text-green-600 dark:text-white">
                          {Math.round((users.reduce((total, user) => total + user.keys.filter(key => key.status === 'activa').length, 0) / users.reduce((total, user) => total + user.keys.length, 0)) * 100) || 0}%
                        </span>
                      </div>
                    </div>
                  </div>
                </div>

                <div className={`p-6 rounded-xl border shadow-lg hover:shadow-2xl hover:-translate-y-1 hover:scale-[1.02] transition-all duration-300 group cursor-pointer ${darkMode ? 'bg-gray-700 border-gray-600' : 'bg-white border-gray-200'}`}>
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center gap-3">
                      <div className="p-2 bg-purple-100 dark:bg-purple-900/30 rounded-lg group-hover:scale-125 group-hover:rotate-12 transition-all duration-500">
                        <Activity size={20} className="text-purple-500 group-hover:text-purple-600" />
                      </div>
                      <span className={`font-semibold group-hover:text-purple-600 transition-colors duration-300 whitespace-nowrap ${darkMode ? 'text-white' : 'text-gray-900'}`}>Usuarios Activos</span>
                    </div>
                  </div>

                  <div className="flex items-end justify-between">
                    <div>
                      <p className="text-3xl font-bold text-purple-600 dark:text-purple-400 mb-1 group-hover:scale-110 transition-transform duration-300">
                        {users.filter(user => user.status === 'activo').length}
                      </p>
                      <p className={`text-sm ${darkMode ? 'text-gray-300' : 'text-gray-600'}`}>
                        Usuarios con estado activo
                      </p>
                    </div>
                    <div className="relative w-16 h-16 group-hover:scale-110 transition-transform duration-300">
                      <svg className="w-16 h-16 transform -rotate-90" viewBox="0 0 36 36">
                        <path
                          d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
                          fill="none"
                          stroke={darkMode ? "#374151" : "#e5e7eb"}
                          strokeWidth="3"
                        />
                        <path
                          d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
                          fill="none"
                          stroke="#9333ea"
                          strokeWidth="3"
                          strokeDasharray={`${Math.round((users.filter(user => user.status === 'activo').length / users.length) * 100) || 0}, 100`}
                          className="transition-all duration-1000 delay-300 group-hover:stroke-purple-600"
                        />
                      </svg>
                      <div className="absolute inset-0 flex items-center justify-center">
                        <span className="text-xs font-bold text-purple-600 dark:text-white">
                          {Math.round((users.filter(user => user.status === 'activo').length / users.length) * 100) || 0}%
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </>
          )}

          {/* Sección de Usuarios */}
          {activeSection === 'users' && (
            <>
              <div className="mb-6">
                <h1 className={`text-lg font-light tracking-wide leading-relaxed mb-6 ${darkMode ? 'text-white' : 'text-gray-700'}`}>
                  <span className={`font-medium ${darkMode ? 'text-white' : 'text-gray-800'}`}>Sistema QRNG Quantum</span> •
                  Gestiona usuarios, llaves cuánticas y configuraciones del sistema con tecnología de vanguardia.
                </h1>

                {/* Botón Crear Nuevo Usuario */}
                <div className="flex justify-center">
                  <button
                    onClick={handleCreateUser}
                    className="flex items-center justify-center gap-3 bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 text-white px-6 py-3 rounded-xl shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300 group"
                  >
                    <div className="p-1 bg-white/20 rounded-lg group-hover:bg-white/30 transition-all duration-300">
                      <User size={18} className="group-hover:rotate-12 transition-transform duration-300" />
                    </div>
                    <span className="font-light tracking-wide">Crear Nuevo Usuario</span>
                  </button>
                </div>
              </div>

              {/* Lista de usuarios */}
              <div className="space-y-3 pb-8">
                {users.map((user) => (
                  <div
                    key={user.id}
                    className={`p-4 rounded-xl border shadow-md hover:shadow-xl hover:-translate-y-1 hover:scale-[1.01] transition-all duration-300 group cursor-pointer ${
                      darkMode ? 'bg-gray-700 border-gray-600' : 'bg-white border-gray-200'
                    }`}
                  >
                    <div className="flex justify-between items-start">
                      <div className="flex-1">
                        <div className="flex items-center gap-4 mb-3">
                          <h3 className="text-lg font-semibold">{user.name}</h3>
                          <span
                            className={`px-3 py-1 rounded-full text-xs font-bold shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300 border-2 ${
                              user.status === 'activo'
                                ? 'bg-green-50 text-green-700 border-green-200 dark:bg-green-900/30 dark:text-green-400 dark:border-green-700'
                                : 'bg-red-50 text-red-700 border-red-200 dark:bg-red-900/30 dark:text-red-400 dark:border-red-700'
                            }`}
                          >
                            {user.status === 'activo' ? 'Activo' : 'Inactivo'}
                          </span>
                        </div>
                        <p className="text-gray-600 dark:text-white mb-3 text-sm">{user.email}</p>

                        {/* Llaves del usuario */}
                        <div>
                          <h4 className="font-medium mb-2 text-sm">Llaves API ({user.keys.length})</h4>
                          <div className="space-y-2">
                            {user.keys.map((key) => (
                              <div
                                key={key.id}
                                className={`p-2 rounded border ${
                                  darkMode ? 'bg-gray-600 border-gray-500' : 'bg-gray-50 border-gray-200'
                                }`}
                              >
                                <div className="flex justify-between items-center">
                                  <div>
                                    <span className="font-mono text-xs">{key.id}</span>
                                    <span className="ml-2 text-xs text-gray-600 dark:text-white">
                                      ({key.name})
                                    </span>
                                  </div>
                                  <span
                                    className={`px-2 py-1 rounded-full text-xs font-bold shadow-sm hover:shadow-md transform hover:scale-105 transition-all duration-300 border ${
                                      key.status === 'activa'
                                        ? 'bg-green-50 text-green-700 border-green-200 dark:bg-green-900/30 dark:text-green-400 dark:border-green-700'
                                        : 'bg-red-50 text-red-700 border-red-200 dark:bg-red-900/30 dark:text-red-400 dark:border-red-700'
                                    }`}
                                  >
                                    {key.status === 'activa' ? 'Activa' : 'Inactiva'}
                                  </span>
                                </div>
                              </div>
                            ))}
                          </div>
                        </div>
                      </div>

                      {/* Botones de acción */}
                      <div className="flex gap-2 ml-4">
                        <button
                          onClick={() => handleEditUser(user)}
                          className="p-2 rounded-lg bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white shadow-md hover:shadow-lg transform hover:scale-110 transition-all duration-300 group"
                          title="Editar usuario"
                        >
                          <Edit size={14} className="group-hover:rotate-12 transition-transform duration-300" />
                        </button>
                        <button
                          onClick={() => handleDeleteUser(user)}
                          className="p-2 rounded-lg bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700 text-white shadow-md hover:shadow-lg transform hover:scale-110 transition-all duration-300 group"
                          title="Eliminar usuario"
                        >
                          <Trash2 size={14} className="group-hover:rotate-12 transition-transform duration-300" />
                        </button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>

            </>
          )}

          {/* Otras secciones placeholder */}
          {activeSection === 'keys' && (
            <div>
              <h1 className={`text-lg font-light tracking-wide leading-relaxed ${darkMode ? 'text-white' : 'text-gray-700'}`}>
                <span className={`font-medium ${darkMode ? 'text-white' : 'text-gray-800'}`}>Sistema QRNG Quantum</span> •
                Gestiona llaves cuánticas y configuraciones del sistema con tecnología de vanguardia.
              </h1>
            </div>
          )}


          </div>
        </div>

        {/* Espaciador para empujar el footer hacia abajo */}
        <div className="flex-1 min-h-[2rem]"></div>

        {/* 🦶 Footer elegante con bordes - siempre abajo */}
        <footer className={`mt-auto p-6 rounded-xl text-center transition-all duration-500 shadow-md border ${
          darkMode
            ? 'bg-gray-800 border-gray-700 text-white'
            : 'bg-white border-gray-200 text-gray-900'
        }`}>
          {/* Logo de Secure Quantum */}
          <div className="flex justify-center items-center gap-3 mb-4">
            <img
              src={darkMode ? "/src/assets/secure-logo-blanco.png" : "/src/assets/sequre-logo-negro.svg"}
              alt="Secure Quantum Logo"
              className="h-8"
            />
          </div>

          {/* Quantum Login System */}
          <div className="mb-4">
            <span className={`text-lg font-light tracking-wide ${
              darkMode ? 'text-white' : 'text-gray-800'
            }`}>
              Quantum Login System
            </span>
          </div>

          {/* Línea final: Empresa + Copyright */}
          <div className="flex justify-center items-center gap-2 text-sm">
            <span className={`font-light ${darkMode ? 'text-gray-300' : 'text-gray-600'}`}>
              Secure Quantum Technology
            </span>
            <span className={darkMode ? 'text-gray-400' : 'text-gray-500'}>•</span>
            <span className={`font-light ${darkMode ? 'text-gray-300' : 'text-gray-600'}`}>
              © 2025
            </span>
          </div>
        </footer>
      </main>

      {/* Modal de Edición */}
      {showEditModal && editingUser && (
        <div className="fixed inset-0 bg-black/60 backdrop-blur-sm flex items-center justify-center z-50 animate-fadeIn">
          <div className={`p-4 sm:p-8 rounded-2xl shadow-2xl max-w-sm sm:max-w-lg w-full mx-4 transform animate-slideUp border ${
            darkMode ? 'bg-gray-800 text-white border-gray-600' : 'bg-white text-gray-900 border-gray-200'
          }`}>
            {/* Header del Modal */}
            <div className="flex justify-between items-start mb-6">
              <div>
                <h3 className={`text-xl font-light tracking-wide leading-relaxed ${darkMode ? 'text-white' : 'text-gray-800'}`}>
                  <span className={`font-medium ${darkMode ? 'text-white' : 'text-gray-900'}`}>Editar Usuario</span> •
                  <span className="text-lg"> Información Personal</span>
                </h3>
              </div>
              <button
                onClick={() => setShowEditModal(false)}
                className={`p-2 rounded-xl transition-all duration-300 hover:scale-110 group ${
                  darkMode ? 'hover:bg-gray-700' : 'hover:bg-gray-100'
                }`}
              >
                <X size={20} className="group-hover:rotate-90 transition-transform duration-300" />
              </button>
            </div>

            {/* Contenido del Modal */}
            <div className={`p-6 rounded-xl border mb-6 ${
              darkMode ? 'bg-gray-700/50 border-gray-600' : 'bg-gray-50 border-gray-200'
            }`}>
              <div className="flex items-center gap-3 mb-4">
                <div className="p-2 bg-blue-100 dark:bg-blue-900/30 rounded-lg">
                  <Edit size={20} className="text-blue-500" />
                </div>
                <span className={`font-medium ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                  Actualizar Datos del Usuario
                </span>
              </div>

              <div className="space-y-4">
                <div>
                  <label className={`block text-sm font-medium mb-2 ${darkMode ? 'text-gray-300' : 'text-gray-600'}`}>
                    Nombre Completo
                  </label>
                  <input
                    type="text"
                    value={editingUser.name}
                    onChange={(e) => handleInputChange('name', e.target.value)}
                    className={`w-full px-4 py-3 border rounded-xl transition-all duration-300 focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                      darkMode
                        ? 'bg-gray-600 border-gray-500 text-white placeholder-gray-400'
                        : 'bg-white border-gray-300 text-gray-900 placeholder-gray-500'
                    }`}
                    placeholder="Ingresa el nombre completo"
                  />
                </div>

                <div>
                  <label className={`block text-sm font-medium mb-2 ${darkMode ? 'text-gray-300' : 'text-gray-600'}`}>
                    Correo Electrónico
                  </label>
                  <input
                    type="email"
                    value={editingUser.email}
                    onChange={(e) => handleInputChange('email', e.target.value)}
                    className={`w-full px-4 py-3 border rounded-xl transition-all duration-300 focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                      darkMode
                        ? 'bg-gray-600 border-gray-500 text-white placeholder-gray-400'
                        : 'bg-white border-gray-300 text-gray-900 placeholder-gray-500'
                    }`}
                    placeholder="<EMAIL>"
                  />
                </div>

                <div>
                  <label className={`block text-sm font-medium mb-2 ${darkMode ? 'text-gray-300' : 'text-gray-600'}`}>
                    Estado del Usuario
                  </label>
                  <select
                    value={editingUser.status}
                    onChange={(e) => handleInputChange('status', e.target.value)}
                    className={`w-full px-4 py-3 border rounded-xl transition-all duration-300 focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                      darkMode
                        ? 'bg-gray-600 border-gray-500 text-white'
                        : 'bg-white border-gray-300 text-gray-900'
                    }`}
                  >
                    <option value="activo">Activo</option>
                    <option value="inactivo">Inactivo</option>
                  </select>
                </div>
              </div>
            </div>

            {/* Botones del Modal */}
            <div className="flex flex-col sm:flex-row gap-4">
              <button
                onClick={saveUserChanges}
                className="flex-1 flex items-center justify-center gap-3 bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white px-6 py-3 rounded-xl shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300 group"
              >
                <div className="p-1 bg-white/20 rounded-lg group-hover:bg-white/30 transition-all duration-300">
                  <Save size={18} className="group-hover:rotate-12 transition-transform duration-300" />
                </div>
                <span className="font-light tracking-wide">Guardar Cambios</span>
              </button>
              <button
                onClick={() => setShowEditModal(false)}
                className={`flex-1 flex items-center justify-center gap-3 px-6 py-3 rounded-xl shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300 group ${
                  darkMode
                    ? 'bg-gradient-to-r from-gray-700 to-gray-800 hover:from-gray-800 hover:to-gray-900 text-white'
                    : 'bg-gradient-to-r from-gray-200 to-gray-300 hover:from-gray-300 hover:to-gray-400 text-gray-900'
                }`}
              >
                <div className={`p-1 rounded-lg transition-all duration-300 ${
                  darkMode ? 'bg-white/10 group-hover:bg-white/20' : 'bg-black/10 group-hover:bg-black/20'
                }`}>
                  <X size={18} className="group-hover:rotate-90 transition-transform duration-300" />
                </div>
                <span className="font-light tracking-wide">Cancelar</span>
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Modal de Confirmación de Eliminación */}
      {showDeleteModal && userToDelete && (
        <div className="fixed inset-0 bg-black/60 backdrop-blur-sm flex items-center justify-center z-50 animate-fadeIn">
          <div className={`p-4 sm:p-8 rounded-2xl shadow-2xl max-w-xs sm:max-w-md w-full mx-4 transform animate-slideUp border ${
            darkMode ? 'bg-gray-800 text-white border-gray-600' : 'bg-white text-gray-900 border-gray-200'
          }`}>
            {/* Header del Modal */}
            <div className="flex justify-between items-start mb-6">
              <div>
                <h3 className={`text-xl font-light tracking-wide leading-relaxed ${darkMode ? 'text-white' : 'text-gray-800'}`}>
                  <span className={`font-medium text-red-600 dark:text-red-400`}>Eliminar Usuario</span> •
                  <span className="text-lg"> Confirmación</span>
                </h3>
              </div>
              <button
                onClick={() => setShowDeleteModal(false)}
                className={`p-2 rounded-xl transition-all duration-300 hover:scale-110 group ${
                  darkMode ? 'hover:bg-gray-700' : 'hover:bg-gray-100'
                }`}
              >
                <X size={20} className="group-hover:rotate-90 transition-transform duration-300" />
              </button>
            </div>

            {/* Contenido del Modal */}
            <div className={`p-6 rounded-xl border mb-6 ${
              darkMode ? 'bg-gray-700/50 border-gray-600' : 'bg-gray-50 border-gray-200'
            }`}>
              <div className="flex items-center gap-3 mb-4">
                <div className="p-2 bg-red-100 dark:bg-red-900/30 rounded-lg">
                  <Trash2 size={20} className="text-red-500" />
                </div>
                <span className={`font-medium ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                  ¿Estás seguro?
                </span>
              </div>
              <p className={`text-sm font-light tracking-wide mb-3 ${darkMode ? 'text-gray-300' : 'text-gray-600'}`}>
                ¿Estás seguro de que deseas eliminar al usuario <strong className="text-red-600 dark:text-red-400">{userToDelete.name}</strong>?
              </p>
              <p className={`text-xs font-light tracking-wide ${darkMode ? 'text-gray-400' : 'text-gray-500'}`}>
                Esta acción no se puede deshacer y se eliminarán todos los datos asociados.
              </p>
            </div>

            {/* Botones del Modal */}
            <div className="flex flex-col sm:flex-row gap-4">
              <button
                onClick={confirmDeleteUser}
                className="flex-1 flex items-center justify-center gap-3 bg-gradient-to-r from-red-600 to-red-700 hover:from-red-700 hover:to-red-800 text-white px-6 py-3 rounded-xl shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300 group"
              >
                <div className="p-1 bg-white/20 rounded-lg group-hover:bg-white/30 transition-all duration-300">
                  <Trash2 size={18} className="group-hover:rotate-12 transition-transform duration-300" />
                </div>
                <span className="font-light tracking-wide">Sí, Eliminar</span>
              </button>
              <button
                onClick={() => setShowDeleteModal(false)}
                className={`flex-1 flex items-center justify-center gap-3 px-6 py-3 rounded-xl shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300 group ${
                  darkMode
                    ? 'bg-gradient-to-r from-gray-700 to-gray-800 hover:from-gray-800 hover:to-gray-900 text-white'
                    : 'bg-gradient-to-r from-gray-200 to-gray-300 hover:from-gray-300 hover:to-gray-400 text-gray-900'
                }`}
              >
                <div className={`p-1 rounded-lg transition-all duration-300 ${
                  darkMode ? 'bg-white/10 group-hover:bg-white/20' : 'bg-black/10 group-hover:bg-black/20'
                }`}>
                  <X size={18} className="group-hover:rotate-90 transition-transform duration-300" />
                </div>
                <span className="font-light tracking-wide">Cancelar</span>
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Modal de Crear Nuevo Usuario */}
      {showCreateModal && (
        <div className="fixed inset-0 bg-black/60 backdrop-blur-sm flex items-center justify-center z-50 animate-fadeIn">
          <div className={`p-4 sm:p-8 rounded-2xl shadow-2xl max-w-4xl w-full mx-4 transform animate-slideUp border ${
            darkMode ? 'bg-gray-800 text-white border-gray-600' : 'bg-white text-gray-900 border-gray-200'
          }`}>
            {/* Header del Modal */}
            <div className="flex justify-between items-start mb-6">
              <div>
                <h3 className={`text-xl font-light tracking-wide leading-relaxed ${darkMode ? 'text-white' : 'text-gray-800'}`}>
                  <span className={`font-medium ${darkMode ? 'text-white' : 'text-gray-900'}`}>Crear Nuevo Usuario</span>
                </h3>
              </div>
              <button
                onClick={() => setShowCreateModal(false)}
                className={`p-2 rounded-xl transition-all duration-300 hover:scale-110 group ${
                  darkMode ? 'hover:bg-gray-700' : 'hover:bg-gray-100'
                }`}
              >
                <X size={20} className="group-hover:rotate-90 transition-transform duration-300" />
              </button>
            </div>

            {/* Contenido del Modal en dos columnas */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              {/* Columna Izquierda - Información Personal */}
              <div className={`p-6 rounded-xl border ${
                darkMode ? 'bg-gray-700/50 border-gray-600' : 'bg-gray-50 border-gray-200'
              }`}>
                <h4 className={`text-lg font-medium mb-4 ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                  Información Personal
                </h4>
                <div className="space-y-4">
                  <div>
                    <label className={`block text-sm font-medium mb-2 ${darkMode ? 'text-gray-300' : 'text-gray-600'}`}>
                      Nombre <span className="text-red-500">*</span>
                    </label>
                    <input
                      type="text"
                      value={newUser.name}
                      onChange={(e) => handleNewUserInputChange('name', e.target.value)}
                      className={`w-full px-4 py-3 border rounded-xl transition-all duration-300 focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                        darkMode
                          ? 'bg-gray-600 border-gray-500 text-white placeholder-gray-400'
                          : 'bg-white border-gray-300 text-gray-900 placeholder-gray-500'
                      }`}
                      placeholder="Ingresa el nombre"
                    />
                  </div>

                  <div>
                    <label className={`block text-sm font-medium mb-2 ${darkMode ? 'text-gray-300' : 'text-gray-600'}`}>
                      Apellido <span className="text-red-500">*</span>
                    </label>
                    <input
                      type="text"
                      value={newUser.apellido}
                      onChange={(e) => handleNewUserInputChange('apellido', e.target.value)}
                      className={`w-full px-4 py-3 border rounded-xl transition-all duration-300 focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                        darkMode
                          ? 'bg-gray-600 border-gray-500 text-white placeholder-gray-400'
                          : 'bg-white border-gray-300 text-gray-900 placeholder-gray-500'
                      }`}
                      placeholder="Ingresa el apellido"
                    />
                  </div>

                  <div>
                    <label className={`block text-sm font-medium mb-2 ${darkMode ? 'text-gray-300' : 'text-gray-600'}`}>
                      Email <span className="text-red-500">*</span>
                    </label>
                    <input
                      type="email"
                      value={newUser.email}
                      onChange={(e) => handleNewUserInputChange('email', e.target.value)}
                      className={`w-full px-4 py-3 border rounded-xl transition-all duration-300 focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                        darkMode
                          ? 'bg-gray-600 border-gray-500 text-white placeholder-gray-400'
                          : 'bg-white border-gray-300 text-gray-900 placeholder-gray-500'
                      }`}
                      placeholder="<EMAIL>"
                    />
                  </div>

                  <div>
                    <label className={`block text-sm font-medium mb-2 ${darkMode ? 'text-gray-300' : 'text-gray-600'}`}>
                      Contraseña <span className="text-red-500">*</span>
                    </label>
                    <input
                      type="password"
                      value={newUser.password}
                      onChange={(e) => handleNewUserInputChange('password', e.target.value)}
                      className={`w-full px-4 py-3 border rounded-xl transition-all duration-300 focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                        darkMode
                          ? 'bg-gray-600 border-gray-500 text-white placeholder-gray-400'
                          : 'bg-white border-gray-300 text-gray-900 placeholder-gray-500'
                      }`}
                      placeholder="Ingresa la contraseña"
                    />
                  </div>

                  <div>
                    <label className={`block text-sm font-medium mb-2 ${darkMode ? 'text-gray-300' : 'text-gray-600'}`}>
                      Empresa
                    </label>
                    <input
                      type="text"
                      value={newUser.empresa}
                      onChange={(e) => handleNewUserInputChange('empresa', e.target.value)}
                      className={`w-full px-4 py-3 border rounded-xl transition-all duration-300 focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                        darkMode
                          ? 'bg-gray-600 border-gray-500 text-white placeholder-gray-400'
                          : 'bg-white border-gray-300 text-gray-900 placeholder-gray-500'
                      }`}
                      placeholder="Nombre de la empresa"
                    />
                  </div>

                  <div>
                    <label className={`block text-sm font-medium mb-2 ${darkMode ? 'text-gray-300' : 'text-gray-600'}`}>
                      Rol
                    </label>
                    <select
                      value={newUser.rol}
                      onChange={(e) => handleNewUserInputChange('rol', e.target.value)}
                      className={`w-full px-4 py-3 border rounded-xl transition-all duration-300 focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                        darkMode
                          ? 'bg-gray-600 border-gray-500 text-white'
                          : 'bg-white border-gray-300 text-gray-900'
                      }`}
                    >
                      <option value="Usuario">Usuario</option>
                      <option value="Administrador">Administrador</option>
                    </select>
                  </div>
                </div>
              </div>

              {/* Columna Derecha - Configuración de Servicios */}
              <div className={`p-6 rounded-xl border ${
                darkMode ? 'bg-gray-700/50 border-gray-600' : 'bg-gray-50 border-gray-200'
              }`}>
                <h4 className={`text-lg font-medium mb-4 ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                  Configuración de Servicios
                </h4>
                <div className="space-y-6">
                  {/* Cipher Trust Manager */}
                  <div>
                    <h5 className={`text-sm font-medium mb-3 text-blue-500`}>
                      Cipher Trust Manager
                    </h5>
                    <div className="space-y-3">
                      <div>
                        <label className={`block text-xs font-medium mb-1 ${darkMode ? 'text-gray-400' : 'text-gray-500'}`}>
                          IP Address
                        </label>
                        <input
                          type="text"
                          defaultValue="https://ctm.example.com:443"
                          className={`w-full px-3 py-2 text-sm border rounded-lg ${
                            darkMode
                              ? 'bg-gray-600 border-gray-500 text-white'
                              : 'bg-white border-gray-300 text-gray-900'
                          }`}
                        />
                      </div>
                      <div className="grid grid-cols-2 gap-3">
                        <div>
                          <label className={`block text-xs font-medium mb-1 ${darkMode ? 'text-gray-400' : 'text-gray-500'}`}>
                            Username
                          </label>
                          <input
                            type="text"
                            className={`w-full px-3 py-2 text-sm border rounded-lg ${
                              darkMode
                                ? 'bg-gray-600 border-gray-500 text-white'
                                : 'bg-white border-gray-300 text-gray-900'
                            }`}
                          />
                        </div>
                        <div>
                          <label className={`block text-xs font-medium mb-1 ${darkMode ? 'text-gray-400' : 'text-gray-500'}`}>
                            Password
                          </label>
                          <input
                            type="password"
                            className={`w-full px-3 py-2 text-sm border rounded-lg ${
                              darkMode
                                ? 'bg-gray-600 border-gray-500 text-white'
                                : 'bg-white border-gray-300 text-gray-900'
                            }`}
                          />
                        </div>
                      </div>
                      <div>
                        <label className={`block text-xs font-medium mb-1 ${darkMode ? 'text-gray-400' : 'text-gray-500'}`}>
                          Domain
                        </label>
                        <input
                          type="text"
                          defaultValue="root"
                          className={`w-full px-3 py-2 text-sm border rounded-lg ${
                            darkMode
                              ? 'bg-gray-600 border-gray-500 text-white'
                              : 'bg-white border-gray-300 text-gray-900'
                          }`}
                        />
                      </div>
                    </div>
                  </div>

                  {/* SEQRNG */}
                  <div>
                    <h5 className={`text-sm font-medium mb-3 text-green-500`}>
                      SEQRNG
                    </h5>
                    <div className="space-y-3">
                      <div>
                        <label className={`block text-xs font-medium mb-1 ${darkMode ? 'text-gray-400' : 'text-gray-500'}`}>
                          IP Address
                        </label>
                        <input
                          type="text"
                          defaultValue="https://seqrng.example.com:1982"
                          className={`w-full px-3 py-2 text-sm border rounded-lg ${
                            darkMode
                              ? 'bg-gray-600 border-gray-500 text-white'
                              : 'bg-white border-gray-300 text-gray-900'
                          }`}
                        />
                      </div>
                      <div>
                        <label className={`block text-xs font-medium mb-1 ${darkMode ? 'text-gray-400' : 'text-gray-500'}`}>
                          API Token
                        </label>
                        <input
                          type="text"
                          defaultValue="1|cHovzUWTeAPUeATIZYeIx3XFf92"
                          className={`w-full px-3 py-2 text-sm border rounded-lg font-mono ${
                            darkMode
                              ? 'bg-gray-600 border-gray-500 text-white'
                              : 'bg-white border-gray-300 text-gray-900'
                          }`}
                        />
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Botones del Modal */}
            <div className="flex flex-col sm:flex-row gap-4 mt-8">
              <button
                onClick={createNewUser}
                className="flex-1 flex items-center justify-center gap-3 bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 text-white px-6 py-3 rounded-xl shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300 group"
              >
                <div className="p-1 bg-white/20 rounded-lg group-hover:bg-white/30 transition-all duration-300">
                  <User size={18} className="group-hover:rotate-12 transition-transform duration-300" />
                </div>
                <span className="font-light tracking-wide">Crear Usuario</span>
              </button>
              <button
                onClick={() => setShowCreateModal(false)}
                className={`flex-1 flex items-center justify-center gap-3 px-6 py-3 rounded-xl shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300 group ${
                  darkMode
                    ? 'bg-gradient-to-r from-gray-700 to-gray-800 hover:from-gray-800 hover:to-gray-900 text-white'
                    : 'bg-gradient-to-r from-gray-200 to-gray-300 hover:from-gray-300 hover:to-gray-400 text-gray-900'
                }`}
              >
                <div className={`p-1 rounded-lg transition-all duration-300 ${
                  darkMode ? 'bg-white/10 group-hover:bg-white/20' : 'bg-black/10 group-hover:bg-black/20'
                }`}>
                  <X size={18} className="group-hover:rotate-90 transition-transform duration-300" />
                </div>
                <span className="font-light tracking-wide">Cancelar</span>
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Modal de Confirmación de Logout */}
      {showLogoutConfirm && (
        <div className="fixed inset-0 bg-black/60 backdrop-blur-sm flex items-center justify-center z-50 animate-fadeIn">
          <div className={`p-4 sm:p-8 rounded-2xl shadow-2xl max-w-xs sm:max-w-md w-full mx-4 transform animate-slideUp border ${
            darkMode ? 'bg-gray-800 text-white border-gray-600' : 'bg-white text-gray-900 border-gray-200'
          }`}>
            {/* Header del Modal */}
            <div className="flex items-center justify-between mb-6">
              <div>
                <h3 className={`text-lg font-light tracking-wide leading-relaxed ${darkMode ? 'text-white' : 'text-gray-700'}`}>
                  <span className={`font-medium ${darkMode ? 'text-white' : 'text-gray-900'}`}>Cerrar Sesión</span> •
                  <span className="text-lg"> Confirmación</span>
                </h3>
              </div>
              <button
                onClick={() => setShowLogoutConfirm(false)}
                className={`p-2 rounded-xl transition-all duration-300 hover:scale-110 group ${
                  darkMode ? 'hover:bg-gray-700' : 'hover:bg-gray-100'
                }`}
              >
                <X size={20} className="group-hover:rotate-90 transition-transform duration-300" />
              </button>
            </div>

            {/* Contenido del Modal */}
            <div className="mb-8">
              <div className="flex items-center gap-4 mb-4">
                <div className="p-3 bg-red-100 dark:bg-red-900/30 rounded-xl">
                  <X size={24} className="text-red-600 dark:text-red-400" />
                </div>
                <div>
                  <h4 className={`font-semibold text-lg ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                    ¿Estás seguro de cerrar sesión?
                  </h4>
                  <p className={`text-sm ${darkMode ? 'text-gray-300' : 'text-gray-600'}`}>
                    Se cerrará tu sesión de administrador
                  </p>
                </div>
              </div>
            </div>

            {/* Botones del Modal */}
            <div className="flex flex-col sm:flex-row gap-4">
              <button
                onClick={handleLogout}
                className="flex-1 flex items-center justify-center gap-3 bg-gradient-to-r from-red-600 to-red-700 hover:from-red-700 hover:to-red-800 text-white px-6 py-3 rounded-xl shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300 group"
              >
                <div className="p-1 bg-white/20 rounded-lg group-hover:bg-white/30 transition-all duration-300">
                  <X size={18} className="group-hover:rotate-90 transition-transform duration-300" />
                </div>
                <span className="font-light tracking-wide">Sí, Cerrar Sesión</span>
              </button>
              <button
                onClick={() => setShowLogoutConfirm(false)}
                className={`flex-1 flex items-center justify-center gap-3 px-6 py-3 rounded-xl shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300 group ${
                  darkMode
                    ? 'bg-gradient-to-r from-gray-700 to-gray-800 hover:from-gray-800 hover:to-gray-900 text-white'
                    : 'bg-gradient-to-r from-gray-200 to-gray-300 hover:from-gray-300 hover:to-gray-400 text-gray-900'
                }`}
              >
                <div className={`p-1 rounded-lg transition-all duration-300 ${
                  darkMode ? 'bg-white/10 group-hover:bg-white/20' : 'bg-black/10 group-hover:bg-black/20'
                }`}>
                  <X size={18} className="group-hover:rotate-90 transition-transform duration-300" />
                </div>
                <span className="font-light tracking-wide">Cancelar</span>
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default AdminDashboard;
